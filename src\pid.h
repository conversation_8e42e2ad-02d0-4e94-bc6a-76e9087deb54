#ifndef PID_H
#define PID_H

#include "config.h"

class PIDController {
private:
  float kp, ki, kd;
  float integral, previousError;
  float integralLimit;
  unsigned long lastTime;
  
public:
  PIDController(float p = 0.0, float i = 0.0, float d = 0.0);
  
  void setGains(float p, float i, float d);
  void setIntegralLimit(float limit);
  float compute(float setpoint, float input);
  void reset();
  
  // Getters
  float getKp() { return kp; }
  float getKi() { return ki; }
  float getKd() { return kd; }
};

class FlightPIDManager {
private:
  PIDController rollPID;
  PIDController pitchPID;
  PIDController yawPID;
  PIDController altitudePID;
  
  // Motor outputs
  int motorFL, motorFR, motorBL, motorBR;
  
public:
  FlightPIDManager();
  
  void initialize();
  void update(float rollSetpoint, float pitchSetpoint, float yawSetpoint, 
              float altitudeSetpoint, int throttle);
  void updateMotors();
  
  // PID tuning
  void setRollPID(float p, float i, float d);
  void setPitchPID(float p, float i, float d);
  void setYawPID(float p, float i, float d);
  void setAltitudePID(float p, float i, float d);
  
  // Motor control
  void armMotors();
  void disarmMotors();
  bool isArmed();
  
  // Getters
  int getMotorFL() { return motorFL; }
  int getMotorFR() { return motorFR; }
  int getMotorBL() { return motorBL; }
  int getMotorBR() { return motorBR; }

  // PID getters
  PIDController& getRollPID() { return rollPID; }
  PIDController& getPitchPID() { return pitchPID; }
  PIDController& getYawPID() { return yawPID; }
  PIDController& getAltitudePID() { return altitudePID; }

  // PID reset methods
  void resetPIDs();
  
private:
  void motorMixing(float roll, float pitch, float yaw, int throttle);
  void constrainMotors();
  
  bool armed;
};

extern FlightPIDManager flightPID;

#endif

