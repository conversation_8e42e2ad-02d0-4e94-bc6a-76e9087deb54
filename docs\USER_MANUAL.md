# QuadFly v2.0 User Manual

## Table of Contents
1. [Getting Started](#getting-started)
2. [Web Interface Guide](#web-interface-guide)
3. [Flight Modes](#flight-modes)
4. [PID Tuning](#pid-tuning)
5. [Safety Systems](#safety-systems)
6. [Troubleshooting](#troubleshooting)
7. [Maintenance](#maintenance)

## Getting Started

### Power On Sequence
1. **Connect battery** to power distribution board
2. **Wait for initialization** (LED will blink during startup)
3. **Check display** for system status
4. **Connect to WiFi** network "QuadFly_Config"
5. **Open web interface** at http://192.168.4.1

### Pre-flight Checklist
- [ ] Battery voltage > 11.1V (3S) or 14.8V (4S)
- [ ] All sensors showing green status
- [ ] GPS lock acquired (if using GPS modes)
- [ ] RC transmitter on and bound
- [ ] Propellers secure and balanced
- [ ] Flight area clear of obstacles
- [ ] Emergency procedures reviewed

### Basic Operation
1. **Turn on transmitter** first
2. **Power on aircraft** second
3. **Wait for system ready** (solid LED)
4. **Arm motors** (throttle low + yaw right for 2 seconds)
5. **Gradually increase throttle** for takeoff
6. **Disarm motors** after landing (throttle low + yaw left)

## Web Interface Guide

### Dashboard Overview
The dashboard provides real-time telemetry and system status:

#### Attitude Display
- **Roll**: Aircraft rotation around front-to-back axis
- **Pitch**: Aircraft rotation around left-to-right axis  
- **Yaw**: Aircraft rotation around vertical axis
- **Values**: Displayed in degrees (-180° to +180°)

#### Battery Monitor
- **Voltage**: Current battery voltage
- **Percentage**: Estimated remaining capacity
- **Color coding**: Green (>50%), Yellow (20-50%), Red (<20%)

#### GPS Status
- **Satellites**: Number of satellites in view
- **Coordinates**: Current latitude/longitude
- **Altitude**: GPS-derived altitude
- **Lock status**: Indicates GPS fix quality

#### RC Inputs
- **Channel values**: 1000-2000 microseconds
- **Throttle**: Vertical control
- **Roll/Pitch/Yaw**: Attitude control
- **AUX channels**: Mode switching and additional functions

#### Safety Indicators
- **Armed status**: Shows if motors are armed
- **Failsafe status**: Indicates RC signal health
- **Flight mode**: Current active flight mode

### PID Tuning Tab
Adjust control system parameters for optimal flight performance:

#### Roll PID
- **P Gain**: Proportional response (1.0-3.0 typical)
- **I Gain**: Integral response (0.05-0.2 typical)
- **D Gain**: Derivative response (0.01-0.1 typical)

#### Pitch PID
- **Similar to Roll**: Usually identical values
- **Fine tuning**: May need slight adjustments

#### Yaw PID
- **Higher P gain**: Typically 2-4x roll/pitch P
- **Lower D gain**: Often zero or very small
- **Moderate I gain**: Helps with heading hold

### Flight Modes Tab
Select and configure different flight modes:

#### Mode Selection
- **Radio buttons**: Click to select desired mode
- **Real-time switching**: Changes take effect immediately
- **Mode description**: Explains current mode behavior

#### Mode Configuration
- **Rate limits**: Maximum rotation rates
- **Angle limits**: Maximum tilt angles
- **Sensitivity**: Control response scaling

### Calibration Tab
Perform sensor calibration procedures:

#### Gyroscope Calibration
1. Place aircraft on level, stable surface
2. Ensure complete stillness
3. Click "Calibrate Gyroscope"
4. Wait for completion message

#### Accelerometer Calibration
1. Position aircraft in 6 orientations
2. Hold each position steady for 10 seconds
3. Follow on-screen instructions
4. Complete all orientations

#### Magnetometer Calibration
1. Move away from metal objects
2. Slowly rotate aircraft in all axes
3. Continue for full 360° in each axis
4. Avoid magnetic interference

### Settings Tab
System configuration and maintenance:

#### System Information
- **Firmware version**: Current software version
- **Uptime**: Time since last restart
- **Memory usage**: Available RAM
- **Loop frequency**: Control loop performance

#### System Actions
- **Reboot**: Restart the flight controller
- **Factory reset**: Restore default settings
- **Save configuration**: Store current settings
- **Load configuration**: Restore saved settings

## Flight Modes

### Manual Mode
**Description**: Direct pilot control with no stabilization assistance.

**Characteristics**:
- No auto-leveling
- Direct stick-to-motor mapping
- Requires constant pilot input
- Most responsive but least stable

**Use Cases**:
- Experienced pilots only
- Aerobatic maneuvers
- Emergency situations
- Testing motor responses

**Stick Inputs**:
- **Throttle**: Direct motor power
- **Roll/Pitch**: Direct attitude control
- **Yaw**: Direct rotation control

### Stabilize Mode
**Description**: Auto-leveling assistance with pilot override capability.

**Characteristics**:
- Returns to level when sticks centered
- Pilot can override auto-leveling
- Good balance of control and stability
- Recommended for most flying

**Use Cases**:
- Normal flight operations
- Learning to fly
- Smooth video recording
- Windy conditions

**Stick Inputs**:
- **Throttle**: Vertical speed control
- **Roll/Pitch**: Target angle (±30° typical)
- **Yaw**: Rotation rate control

### Altitude Hold Mode
**Description**: Maintains current altitude using barometric sensor.

**Characteristics**:
- Automatic altitude maintenance
- Throttle controls climb/descent rate
- Includes stabilize mode features
- Requires barometric sensor

**Use Cases**:
- Hands-off hovering
- Consistent altitude flight
- Aerial photography
- Reduced pilot workload

**Stick Inputs**:
- **Throttle**: Altitude adjustment
- **Roll/Pitch**: Horizontal movement
- **Yaw**: Heading control

**Limitations**:
- Affected by air pressure changes
- May drift in windy conditions
- Requires initial altitude reference

### GPS Hold Mode
**Description**: Maintains position and altitude using GPS and barometer.

**Characteristics**:
- Automatic position holding
- Returns to hold position when released
- Includes altitude hold features
- Requires GPS lock (6+ satellites)

**Use Cases**:
- Precision hovering
- Waypoint navigation
- Return-to-home functionality
- Automated flight patterns

**Stick Inputs**:
- **Throttle**: Altitude adjustment
- **Roll/Pitch**: Position adjustment
- **Yaw**: Heading control

**Requirements**:
- GPS lock with 6+ satellites
- Clear sky view
- Minimal magnetic interference
- Proper compass calibration

### Acro Mode
**Description**: Direct angular rate control for aerobatic flying.

**Characteristics**:
- No angle limits
- Direct rate control
- No auto-leveling
- Maximum agility

**Use Cases**:
- Aerobatic maneuvers
- Racing applications
- Advanced pilot training
- Inverted flight

**Stick Inputs**:
- **Throttle**: Motor power
- **Roll/Pitch/Yaw**: Angular rates (°/sec)

**Safety Notes**:
- Expert pilots only
- Requires constant attention
- Easy to lose orientation
- High crash risk if inexperienced

## PID Tuning

### Understanding PID Control
PID (Proportional-Integral-Derivative) control maintains desired aircraft attitude:

- **P (Proportional)**: Immediate response to error
- **I (Integral)**: Corrects steady-state errors
- **D (Derivative)**: Dampens oscillations

### Tuning Process

#### Step 1: Start with Defaults
Begin with conservative default values:
- Roll/Pitch P: 1.0, I: 0.1, D: 0.05
- Yaw P: 2.0, I: 0.2, D: 0.0

#### Step 2: Adjust P Gain
1. **Increase P** until aircraft responds quickly to stick inputs
2. **Too low**: Sluggish response, poor tracking
3. **Too high**: Oscillations, overshooting
4. **Optimal**: Quick response without oscillation

#### Step 3: Adjust D Gain
1. **Increase D** to reduce oscillations from P gain
2. **Too low**: Oscillations continue
3. **Too high**: Jittery, nervous behavior
4. **Optimal**: Smooth, damped response

#### Step 4: Adjust I Gain
1. **Increase I** to eliminate steady-state errors
2. **Too low**: Aircraft drifts, doesn't hold position
3. **Too high**: Slow oscillations, overshoot
4. **Optimal**: Holds position without drift

### Tuning Tips

#### Visual Indicators
- **Good tune**: Smooth, responsive, stable hover
- **P too high**: Fast oscillations, twitchy
- **P too low**: Sluggish, poor tracking
- **I too high**: Slow oscillations, bouncing
- **D too high**: Jittery, high-frequency noise

#### Flight Test Procedure
1. **Hover test**: Check stability in calm air
2. **Step input**: Quick stick movements
3. **Disturbance rejection**: Push aircraft gently
4. **Wind test**: Fly in light wind conditions
5. **Rate test**: Fast roll/pitch maneuvers

#### Common Problems
- **Oscillations**: Reduce P gain or increase D gain
- **Drift**: Increase I gain gradually
- **Sluggish**: Increase P gain
- **Overshoot**: Increase D gain or reduce P gain

## Safety Systems

### Failsafe Protection
Automatic safety responses to system failures:

#### RC Signal Loss
- **Detection**: No valid RC signal for 1 second
- **Response**: Immediate motor disarm
- **Recovery**: Normal operation when signal returns
- **Prevention**: Check transmitter battery, antenna

#### Low Battery Protection
- **Warning level**: 10.5V (3S) or 14.0V (4S)
- **Critical level**: 9.5V (3S) or 12.6V (4S)
- **Response**: Warning alerts, forced landing
- **Prevention**: Monitor battery voltage, set timers

#### Sensor Failure Detection
- **IMU failure**: Erratic attitude readings
- **GPS failure**: Loss of position fix
- **Barometer failure**: Altitude drift
- **Response**: Mode restrictions, warnings

### Arming System
Prevents accidental motor activation:

#### Arming Requirements
- Throttle at minimum position
- No active failsafe conditions
- All sensors operational
- Battery voltage adequate

#### Arming Procedure
1. **Throttle low** (< 1100µs)
2. **Yaw right** (> 1900µs)
3. **Hold for 2 seconds**
4. **Motors arm** (LED changes, beep)

#### Disarming Procedure
1. **Throttle low** (< 1050µs)
2. **Yaw left** (< 1100µs)
3. **Hold for 1 second**
4. **Motors disarm** immediately

### Emergency Procedures

#### Immediate Disarm
- **When**: Loss of control, crash imminent
- **Action**: Throttle low + yaw left
- **Result**: Instant motor shutdown
- **Risk**: Aircraft will fall immediately

#### Failsafe Landing
- **When**: RC signal lost with GPS
- **Action**: Automatic descent at current position
- **Rate**: 1 m/s descent speed
- **Completion**: Motors disarm at ground level

#### Battery Emergency
- **When**: Critical battery voltage
- **Action**: Immediate landing sequence
- **Warning**: Audible alerts, display warnings
- **Prevention**: Regular battery monitoring

## Troubleshooting

### Common Flight Issues

#### Aircraft Won't Arm
**Symptoms**: Motors don't respond to arming sequence
**Causes**:
- Throttle not at minimum
- Failsafe active
- Sensor failure
- Low battery

**Solutions**:
1. Check throttle position (should be < 1100µs)
2. Verify RC signal strength
3. Check sensor status in web interface
4. Ensure battery voltage > 11.1V

#### Unstable Hover
**Symptoms**: Aircraft oscillates, difficult to control
**Causes**:
- Poor PID tuning
- Vibration issues
- Unbalanced propellers
- Center of gravity problems

**Solutions**:
1. Reduce P gains by 20%
2. Check motor mounts for vibration
3. Balance all propellers
4. Verify center of gravity location

#### GPS Won't Lock
**Symptoms**: GPS shows 0 satellites or no fix
**Causes**:
- Obstructed sky view
- Magnetic interference
- Cold start delay
- Hardware failure

**Solutions**:
1. Move to open area with clear sky
2. Wait 5-15 minutes for cold start
3. Check GPS antenna connection
4. Verify GPS module power supply

#### Web Interface Issues
**Symptoms**: Can't connect to configuration page
**Causes**:
- WiFi connection problems
- IP address conflicts
- Browser compatibility
- ESP32 crash

**Solutions**:
1. Reconnect to "QuadFly_Config" network
2. Try different browser
3. Clear browser cache
4. Restart flight controller

### Diagnostic Tools

#### Serial Monitor
Connect USB cable and open serial monitor (115200 baud):
- System startup messages
- Sensor readings
- Error messages
- Performance statistics

#### Web Interface Diagnostics
- Real-time sensor data
- System status indicators
- Error logs
- Performance metrics

#### LED Status Codes
- **Solid**: System ready
- **Slow blink**: Initializing
- **Fast blink**: Error condition
- **Off**: No power or system failure

## Maintenance

### Regular Maintenance

#### Pre-flight Inspection
- Check all connections
- Verify propeller condition
- Test control surfaces
- Monitor battery voltage
- Clean sensors if needed

#### Post-flight Maintenance
- Check for damage
- Clean aircraft if dirty
- Store battery properly
- Log flight time
- Note any issues

#### Weekly Maintenance
- Calibrate sensors if needed
- Update firmware if available
- Check screw tightness
- Inspect wiring harness
- Test failsafe systems

### Battery Care

#### Charging
- Use appropriate LiPo charger
- Never exceed 1C charge rate
- Monitor temperature during charging
- Balance charge regularly
- Never leave unattended

#### Storage
- Store at 3.8V per cell (storage voltage)
- Keep in fireproof container
- Avoid extreme temperatures
- Check voltage monthly
- Dispose of damaged batteries properly

#### Usage Guidelines
- Never discharge below 3.0V per cell
- Monitor voltage during flight
- Land when voltage drops to 3.5V per cell
- Allow cooling between flights
- Replace when capacity drops below 80%

### Firmware Updates

#### Checking for Updates
1. Visit project repository
2. Check release notes
3. Compare version numbers
4. Download new firmware

#### Update Process
1. Backup current configuration
2. Download new firmware
3. Compile and upload
4. Restore configuration
5. Test all functions

#### Rollback Procedure
If issues occur after update:
1. Download previous version
2. Upload previous firmware
3. Restore backed-up configuration
4. Report issues to developers

---

**Remember**: Always prioritize safety and follow proper procedures. When in doubt, land immediately and investigate the issue on the ground.

