<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuadFly Flight Controller v2.0</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-helicopter"></i>
                <h1>QuadFly v2.0</h1>
            </div>
            <div class="status-bar">
                <div class="status-item" id="connection-status">
                    <i class="fas fa-wifi"></i>
                    <span>Connected</span>
                </div>
                <div class="status-item" id="armed-status">
                    <i class="fas fa-shield-alt"></i>
                    <span>Disarmed</span>
                </div>
                <div class="status-item" id="flight-mode">
                    <i class="fas fa-plane"></i>
                    <span>Manual</span>
                </div>
            </div>
        </header>

        <nav class="nav-tabs">
            <button class="tab-button active" data-tab="dashboard">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard
            </button>
            <button class="tab-button" data-tab="pid">
                <i class="fas fa-cogs"></i>
                PID Tuning
            </button>
            <button class="tab-button" data-tab="modes">
                <i class="fas fa-list"></i>
                Flight Modes
            </button>
            <button class="tab-button" data-tab="calibration">
                <i class="fas fa-compass"></i>
                Calibration
            </button>
            <button class="tab-button" data-tab="settings">
                <i class="fas fa-wrench"></i>
                Settings
            </button>
        </nav>

        <main class="main-content">
            <!-- Dashboard Tab -->
            <div class="tab-content active" id="dashboard">
                <div class="dashboard-grid">
                    <div class="card">
                        <h3><i class="fas fa-compass"></i> Attitude</h3>
                        <div class="attitude-display">
                            <div class="attitude-item">
                                <label>Roll:</label>
                                <span id="roll-value">0.0°</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="roll-bar"></div>
                                </div>
                            </div>
                            <div class="attitude-item">
                                <label>Pitch:</label>
                                <span id="pitch-value">0.0°</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="pitch-bar"></div>
                                </div>
                            </div>
                            <div class="attitude-item">
                                <label>Yaw:</label>
                                <span id="yaw-value">0.0°</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="yaw-bar"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-battery-half"></i> Battery</h3>
                        <div class="battery-display">
                            <div class="battery-icon">
                                <div class="battery-level" id="battery-level"></div>
                            </div>
                            <div class="battery-info">
                                <div class="voltage" id="battery-voltage">12.4V</div>
                                <div class="percentage" id="battery-percentage">85%</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-satellite"></i> GPS</h3>
                        <div class="gps-display">
                            <div class="gps-item">
                                <label>Satellites:</label>
                                <span id="gps-sats">8</span>
                            </div>
                            <div class="gps-item">
                                <label>Latitude:</label>
                                <span id="gps-lat">40.7128</span>
                            </div>
                            <div class="gps-item">
                                <label>Longitude:</label>
                                <span id="gps-lng">-74.0060</span>
                            </div>
                            <div class="gps-item">
                                <label>Altitude:</label>
                                <span id="gps-alt">10.5m</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-chart-line"></i> Sensors</h3>
                        <canvas id="sensor-chart" width="300" height="200"></canvas>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-gamepad"></i> RC Inputs</h3>
                        <div class="rc-display">
                            <div class="rc-channel">
                                <label>Throttle:</label>
                                <div class="rc-bar">
                                    <div class="rc-fill" id="throttle-bar"></div>
                                </div>
                                <span id="throttle-value">1500</span>
                            </div>
                            <div class="rc-channel">
                                <label>Roll:</label>
                                <div class="rc-bar">
                                    <div class="rc-fill" id="roll-rc-bar"></div>
                                </div>
                                <span id="roll-rc-value">1500</span>
                            </div>
                            <div class="rc-channel">
                                <label>Pitch:</label>
                                <div class="rc-bar">
                                    <div class="rc-fill" id="pitch-rc-bar"></div>
                                </div>
                                <span id="pitch-rc-value">1500</span>
                            </div>
                            <div class="rc-channel">
                                <label>Yaw:</label>
                                <div class="rc-bar">
                                    <div class="rc-fill" id="yaw-rc-bar"></div>
                                </div>
                                <span id="yaw-rc-value">1500</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-shield-alt"></i> Safety</h3>
                        <div class="safety-controls">
                            <button class="arm-button" id="arm-button">
                                <i class="fas fa-power-off"></i>
                                ARM MOTORS
                            </button>
                            <div class="safety-status">
                                <div class="safety-item">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span id="failsafe-status">Failsafe: OK</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PID Tuning Tab -->
            <div class="tab-content" id="pid">
                <div class="pid-grid">
                    <div class="card">
                        <h3><i class="fas fa-arrows-alt-h"></i> Roll PID</h3>
                        <div class="pid-controls">
                            <div class="pid-slider">
                                <label>P Gain:</label>
                                <input type="range" id="roll-p" min="0" max="5" step="0.1" value="1.0">
                                <span id="roll-p-value">1.0</span>
                            </div>
                            <div class="pid-slider">
                                <label>I Gain:</label>
                                <input type="range" id="roll-i" min="0" max="1" step="0.01" value="0.1">
                                <span id="roll-i-value">0.1</span>
                            </div>
                            <div class="pid-slider">
                                <label>D Gain:</label>
                                <input type="range" id="roll-d" min="0" max="1" step="0.01" value="0.05">
                                <span id="roll-d-value">0.05</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-arrows-alt-v"></i> Pitch PID</h3>
                        <div class="pid-controls">
                            <div class="pid-slider">
                                <label>P Gain:</label>
                                <input type="range" id="pitch-p" min="0" max="5" step="0.1" value="1.0">
                                <span id="pitch-p-value">1.0</span>
                            </div>
                            <div class="pid-slider">
                                <label>I Gain:</label>
                                <input type="range" id="pitch-i" min="0" max="1" step="0.01" value="0.1">
                                <span id="pitch-i-value">0.1</span>
                            </div>
                            <div class="pid-slider">
                                <label>D Gain:</label>
                                <input type="range" id="pitch-d" min="0" max="1" step="0.01" value="0.05">
                                <span id="pitch-d-value">0.05</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-sync-alt"></i> Yaw PID</h3>
                        <div class="pid-controls">
                            <div class="pid-slider">
                                <label>P Gain:</label>
                                <input type="range" id="yaw-p" min="0" max="5" step="0.1" value="2.0">
                                <span id="yaw-p-value">2.0</span>
                            </div>
                            <div class="pid-slider">
                                <label>I Gain:</label>
                                <input type="range" id="yaw-i" min="0" max="1" step="0.01" value="0.2">
                                <span id="yaw-i-value">0.2</span>
                            </div>
                            <div class="pid-slider">
                                <label>D Gain:</label>
                                <input type="range" id="yaw-d" min="0" max="1" step="0.01" value="0.0">
                                <span id="yaw-d-value">0.0</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-save"></i> PID Actions</h3>
                        <div class="pid-actions">
                            <button class="btn btn-primary" id="save-pid">
                                <i class="fas fa-save"></i>
                                Save PID Settings
                            </button>
                            <button class="btn btn-secondary" id="reset-pid">
                                <i class="fas fa-undo"></i>
                                Reset to Defaults
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Flight Modes Tab -->
            <div class="tab-content" id="modes">
                <div class="modes-grid">
                    <div class="card">
                        <h3><i class="fas fa-list"></i> Flight Mode Selection</h3>
                        <div class="mode-selector">
                            <div class="mode-option">
                                <input type="radio" id="manual-mode" name="flight-mode" value="0">
                                <label for="manual-mode">
                                    <i class="fas fa-hand-paper"></i>
                                    Manual (Passthrough)
                                </label>
                            </div>
                            <div class="mode-option">
                                <input type="radio" id="stabilize-mode" name="flight-mode" value="1" checked>
                                <label for="stabilize-mode">
                                    <i class="fas fa-balance-scale"></i>
                                    Stabilize (Auto-level)
                                </label>
                            </div>
                            <div class="mode-option">
                                <input type="radio" id="altitude-mode" name="flight-mode" value="2">
                                <label for="altitude-mode">
                                    <i class="fas fa-arrows-alt-v"></i>
                                    Altitude Hold
                                </label>
                            </div>
                            <div class="mode-option">
                                <input type="radio" id="gps-mode" name="flight-mode" value="3">
                                <label for="gps-mode">
                                    <i class="fas fa-satellite"></i>
                                    GPS Hold
                                </label>
                            </div>
                            <div class="mode-option">
                                <input type="radio" id="acro-mode" name="flight-mode" value="4">
                                <label for="acro-mode">
                                    <i class="fas fa-fighter-jet"></i>
                                    Acro (Rate Mode)
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-info-circle"></i> Mode Description</h3>
                        <div class="mode-description" id="mode-description">
                            <p>Stabilize mode provides auto-leveling assistance. The aircraft will automatically return to level flight when the sticks are centered.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Calibration Tab -->
            <div class="tab-content" id="calibration">
                <div class="calibration-grid">
                    <div class="card">
                        <h3><i class="fas fa-compass"></i> Sensor Calibration</h3>
                        <div class="calibration-controls">
                            <button class="btn btn-primary" id="calibrate-gyro">
                                <i class="fas fa-sync"></i>
                                Calibrate Gyroscope
                            </button>
                            <button class="btn btn-primary" id="calibrate-accel">
                                <i class="fas fa-arrows-alt"></i>
                                Calibrate Accelerometer
                            </button>
                            <button class="btn btn-primary" id="calibrate-mag">
                                <i class="fas fa-compass"></i>
                                Calibrate Magnetometer
                            </button>
                        </div>
                        <div class="calibration-status" id="calibration-status">
                            Ready for calibration
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-content" id="settings">
                <div class="settings-grid">
                    <div class="card">
                        <h3><i class="fas fa-wrench"></i> System Settings</h3>
                        <div class="settings-controls">
                            <button class="btn btn-warning" id="reboot-system">
                                <i class="fas fa-power-off"></i>
                                Reboot System
                            </button>
                            <button class="btn btn-info" id="factory-reset">
                                <i class="fas fa-undo-alt"></i>
                                Factory Reset
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <h3><i class="fas fa-info"></i> System Information</h3>
                        <div class="system-info">
                            <div class="info-item">
                                <label>Firmware Version:</label>
                                <span>v2.0.0</span>
                            </div>
                            <div class="info-item">
                                <label>Uptime:</label>
                                <span id="uptime">00:05:23</span>
                            </div>
                            <div class="info-item">
                                <label>Free Memory:</label>
                                <span id="free-memory">245KB</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>

