# QuadFly Flight Controller v2.0

A professional-grade open-source flight controller firmware designed for the ESP32 microcontroller. QuadFly v2.0 provides advanced flight control capabilities with real-time sensor fusion, PID control, multiple flight modes, and a modern web-based configuration interface.

## Features

### Core Flight Control
- **Real-time sensor fusion** using complementary filter
- **Advanced PID control system** with per-axis tuning
- **Multiple flight modes**: Manual, Stabilize, Altitude Hold, GPS Hold, Acro
- **250Hz control loop** for responsive flight performance
- **Motor mixing** for X-quad configuration

### Safety Systems
- **Failsafe protection** with RC signal loss detection
- **Arming/disarming logic** with safety checks
- **Battery voltage monitoring** with low voltage warnings
- **Watchdog protection** against system lockups

### User Interface
- **WiFi web interface** for real-time telemetry and configuration
- **TFT display** showing flight status and system information
- **Real-time data visualization** with sensor graphs
- **Mobile-responsive design** for smartphone/tablet access

### Sensor Support
- **MPU6050**: 6-axis accelerometer and gyroscope
- **HMC5883L**: 3-axis magnetometer for heading
- **MS5611**: Barometric pressure sensor for altitude
- **GPS module**: Position hold and navigation (NEO-6M/NEO-M8N)
- **RC receiver**: 8-channel PWM input support

## Hardware Requirements

### Core Components
- **ESP32 Dev Module** (main microcontroller)
- **GY-86 sensor module** (MPU6050 + HMC5883L + MS5611)
- **GPS module** (NEO-6M or NEO-M8N compatible)
- **ST7735 TFT display** (128x160 pixels)
- **8-channel RC receiver** (PWM output)
- **4x ESCs** (Electronic Speed Controllers)
- **4x brushless motors** with propellers
- **LiPo battery** (3S or 4S recommended)

### Additional Components
- **Quadcopter frame** (X-configuration)
- **Power distribution board** (PDB)
- **Voltage regulator** (5V/3.3V for sensors)
- **Capacitors** for power filtering
- **Connectors** and wiring harness

## Pin Configuration

| Function | ESP32 Pin | Description |
|----------|-----------|-------------|
| I2C SDA | 21 | Sensor communication |
| I2C SCL | 22 | Sensor communication |
| RC Throttle | 14 | PWM input |
| RC Roll | 13 | PWM input |
| RC Pitch | 12 | PWM input |
| RC Yaw | 27 | PWM input |
| RC AUX1 | 26 | PWM input |
| RC AUX2 | 25 | PWM input |
| RC AUX3 | 36 | PWM input |
| RC AUX4 | 34 | PWM input |
| Motor FL | 33 | Front Left motor |
| Motor FR | 32 | Front Right motor |
| Motor BL | 15 | Back Left motor |
| Motor BR | 19 | Back Right motor |
| TFT CS | 5 | Display chip select |
| TFT RST | 4 | Display reset |
| TFT DC | 2 | Display data/command |
| Status LED | 2 | System status indicator |
| Battery ADC | 35 | Voltage monitoring |
| GPS RX | 16 | GPS communication |
| GPS TX | 17 | GPS communication |

## Software Architecture

### Modular Design
The firmware is organized into modular components for maintainability and extensibility:

- **config.h**: System configuration and constants
- **pins.h**: Hardware pin definitions
- **sensors.h/.cpp**: Sensor management and fusion
- **pid.h/.cpp**: PID control system
- **flight_modes.h/.cpp**: Flight mode management
- **web_interface.h/.cpp**: WiFi and web server
- **display_ui.h/.cpp**: TFT display management

### Control Flow
1. **Sensor Reading**: IMU, barometer, GPS, and RC inputs
2. **Sensor Fusion**: Combine sensor data for attitude estimation
3. **Flight Mode Processing**: Interpret pilot inputs based on current mode
4. **PID Control**: Calculate motor corrections
5. **Motor Mixing**: Convert control outputs to individual motor commands
6. **Safety Checks**: Monitor for failsafe conditions
7. **Telemetry**: Update web interface and display

## Installation Guide

### Prerequisites
- Arduino IDE 1.8.x or 2.x
- ESP32 board package
- Required libraries (see Library Dependencies)

### Library Dependencies
Install the following libraries through Arduino Library Manager:

```
- MPU6050_tockn
- HMC5883L
- MS5611
- TinyGPS++
- Adafruit_GFX
- Adafruit_ST7735
- ArduinoJson
- ESP32Servo
```

### Hardware Assembly
1. **Mount sensors** on vibration-dampened platform
2. **Connect wiring** according to pin configuration
3. **Install ESCs** and motors on frame
4. **Connect power distribution** with appropriate filtering
5. **Mount GPS** away from interference sources
6. **Install display** in accessible location

### Firmware Upload
1. **Clone repository** or download source code
2. **Open QuadFly_v2.0.ino** in Arduino IDE
3. **Select ESP32 Dev Module** as board type
4. **Configure upload settings**:
   - Upload Speed: 921600
   - CPU Frequency: 240MHz
   - Flash Frequency: 80MHz
   - Flash Mode: DIO
   - Flash Size: 4MB
   - Partition Scheme: Default 4MB
5. **Compile and upload** firmware

### Initial Configuration
1. **Power on** the flight controller
2. **Connect to WiFi** network "QuadFly_Config" (password: quadfly123)
3. **Open web browser** and navigate to ***********
4. **Calibrate sensors** using web interface
5. **Tune PID parameters** for your specific aircraft
6. **Test all flight modes** before first flight

## Web Interface

### Dashboard
- Real-time attitude display (roll, pitch, yaw)
- Battery voltage and percentage
- GPS status and coordinates
- RC input monitoring
- Motor output visualization
- Safety status indicators

### PID Tuning
- Individual P, I, D gain adjustment for each axis
- Real-time parameter updates
- Save/load configuration
- Reset to default values

### Flight Modes
- Mode selection and description
- Current mode indicator
- Mode-specific parameter adjustment

### Calibration
- Gyroscope calibration routine
- Accelerometer calibration
- Magnetometer calibration
- Calibration status feedback

### Settings
- System information display
- Firmware update capability
- Factory reset option
- Configuration backup/restore

## Flight Modes

### Manual (Passthrough)
Direct control with no stabilization assistance. Pilot inputs are passed directly to motors with basic mixing.

### Stabilize (Auto-level)
Provides auto-leveling assistance. Aircraft automatically returns to level flight when sticks are centered.

### Altitude Hold
Maintains current altitude using barometric sensor while providing stabilization assistance.

### GPS Hold
Maintains current position using GPS coordinates while providing altitude hold and stabilization.

### Acro (Rate Mode)
Direct angular rate control for advanced aerobatic maneuvers. No auto-leveling assistance.

## Safety Features

### Failsafe System
- **RC Signal Loss**: Automatically disarms motors if RC signal is lost for more than 1 second
- **Low Battery**: Warns pilot and can trigger emergency landing sequence
- **Sensor Failure**: Detects and responds to sensor malfunctions
- **Watchdog Timer**: Prevents system lockups

### Arming Sequence
Motors can only be armed when:
- Throttle stick is at minimum position
- No failsafe conditions are active
- All sensors are functioning properly
- Battery voltage is adequate

### Emergency Procedures
- **Immediate Disarm**: Throttle low + yaw left
- **Failsafe Landing**: Gradual descent with position hold (if GPS available)
- **Battery Critical**: Automatic disarm to prevent over-discharge

## Troubleshooting

### Common Issues

**Sensors not detected**
- Check I2C connections (SDA/SCL)
- Verify sensor power supply (3.3V)
- Test with I2C scanner sketch

**Motors not responding**
- Verify ESC calibration
- Check motor pin connections
- Ensure proper arming sequence

**Web interface not accessible**
- Confirm WiFi connection to "QuadFly_Config"
- Check IP address (should be ***********)
- Restart ESP32 if connection fails

**GPS not acquiring lock**
- Ensure clear sky view
- Check GPS module connections
- Verify baud rate (9600)
- Allow sufficient time for cold start

**Unstable flight**
- Review PID tuning parameters
- Check propeller balance and condition
- Verify center of gravity
- Ensure proper motor rotation

### Debug Information
Enable debug output through serial monitor (115200 baud) for detailed system information including:
- Loop timing and frequency
- Sensor readings
- PID outputs
- Motor commands
- System status

## Development

### Contributing
Contributions are welcome! Please follow these guidelines:
1. Fork the repository
2. Create feature branch
3. Follow coding standards
4. Add documentation
5. Submit pull request

### Coding Standards
- Use descriptive variable names
- Comment complex algorithms
- Follow Arduino style guide
- Maintain modular structure
- Include error handling

### Testing
- Test all flight modes thoroughly
- Verify safety systems operation
- Check web interface functionality
- Validate sensor calibration
- Perform ground testing before flight

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

**IMPORTANT SAFETY NOTICE**: This is experimental flight controller software. Always follow proper safety procedures:

- Never fly over people or property
- Always maintain visual line of sight
- Test thoroughly in safe environment
- Follow local regulations and laws
- Use appropriate safety equipment
- Have emergency procedures ready

The developers are not responsible for any damage or injury resulting from the use of this software.

## Support

For support, questions, or contributions:
- GitHub Issues: Report bugs and request features
- Documentation: Refer to inline code comments
- Community: Join discussions in project forums

## Changelog

### v2.0.0
- Complete rewrite for ESP32 platform
- Added web-based configuration interface
- Implemented multiple flight modes
- Enhanced safety systems
- Improved sensor fusion algorithm
- Added TFT display support
- Modular code architecture

---

**Happy Flying!** 🚁

