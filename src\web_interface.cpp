#include "web_interface.h"
#include "sensors.h"
#include "pid.h"
#include "flight_modes.h"

WebInterfaceManager webInterface;

WebInterfaceManager::WebInterfaceManager() : server(WEB_SERVER_PORT) {
  lastTelemetryUpdate = 0;
  telemetryUpdateInterval = 100; // 100ms
}

bool WebInterfaceManager::initialize() {
  // Initialize SPIFFS
  if (!setupSPIFFS()) {
    Serial.println("SPIFFS initialization failed!");
    return false;
  }
  
  // Initialize preferences
  preferences.begin("quadfly", false);
  
  // Setup WiFi
  setupWiFi();
  
  // Setup web server routes
  setupRoutes();
  
  // Start server
  server.begin();
  Serial.println("Web server started");
  Serial.print("Access point: ");
  Serial.println(WIFI_SSID);
  Serial.print("IP address: ");
  Serial.println(WiFi.softAPIP());
  
  return true;
}

void WebInterfaceManager::update() {
  server.handleClient();
}

void WebInterfaceManager::setupWiFi() {
  WiFi.mode(WIFI_AP);
  WiFi.softAP(WIFI_SSID, WIFI_PASSWORD);
  
  Serial.println("WiFi Access Point started");
  Serial.print("SSID: ");
  Serial.println(WIFI_SSID);
  Serial.print("Password: ");
  Serial.println(WIFI_PASSWORD);
  Serial.print("IP address: ");
  Serial.println(WiFi.softAPIP());
}

bool WebInterfaceManager::setupSPIFFS() {
  if (!SPIFFS.begin(true)) {
    Serial.println("An error occurred while mounting SPIFFS");
    return false;
  }
  return true;
}

void WebInterfaceManager::setupRoutes() {
  // Serve static files
  server.on("/", HTTP_GET, [this]() { handleRoot(); });
  server.on("/style.css", HTTP_GET, [this]() {
    server.send(200, "text/css", "/* CSS content would be served from SPIFFS */");
  });
  server.on("/script.js", HTTP_GET, [this]() {
    server.send(200, "application/javascript", "/* JS content would be served from SPIFFS */");
  });
  
  // API endpoints
  server.on("/api/telemetry", HTTP_GET, [this]() { handleTelemetry(); });
  server.on("/api/config", HTTP_GET, [this]() { handleConfig(); });
  server.on("/api/pid", HTTP_POST, [this]() { handlePIDUpdate(); });
  server.on("/api/flight-mode", HTTP_POST, [this]() { handleModeChange(); });
  server.on("/api/calibrate", HTTP_POST, [this]() { handleCalibrationStart(); });
  server.on("/api/arm", HTTP_POST, [this]() { handleArmDisarm(); });
  server.on("/api/save-pid", HTTP_POST, [this]() { savePIDSettings(); });
  server.on("/api/reboot", HTTP_POST, [this]() {
    server.send(200, "application/json", "{\"status\":\"rebooting\"}");
    delay(1000);
    ESP.restart();
  });
  server.on("/api/factory-reset", HTTP_POST, [this]() {
    preferences.clear();
    server.send(200, "application/json", "{\"status\":\"factory_reset_complete\"}");
    delay(1000);
    ESP.restart();
  });
  
  server.onNotFound([this]() { handleNotFound(); });
}

void WebInterfaceManager::handleRoot() {
  // In a real implementation, this would serve the HTML file from SPIFFS
  String html = R"HTML(
<!DOCTYPE html>
<html>
<head>
    <title>QuadFly Flight Controller v2.0</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status { display: flex; gap: 20px; margin-bottom: 20px; }
        .status-item { padding: 10px; background: #e3f2fd; border-radius: 4px; }
        .telemetry { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .value { font-weight: bold; color: #1976d2; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #1976d2; color: white; }
        .btn-danger { background: #d32f2f; color: white; }
        .btn-success { background: #388e3c; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>QuadFly Flight Controller v2.0</h1>
        
        <div class="status">
            <div class="status-item">
                <strong>Status:</strong> <span id="status">Connected</span>
            </div>
            <div class="status-item">
                <strong>Mode:</strong> <span id="mode">Stabilize</span>
            </div>
            <div class="status-item">
                <strong>Armed:</strong> <span id="armed">No</span>
            </div>
        </div>
        
        <div class="telemetry">
            <div class="card">
                <h3>Attitude</h3>
                <p>Roll: <span class="value" id="roll">0.0°</span></p>
                <p>Pitch: <span class="value" id="pitch">0.0°</span></p>
                <p>Yaw: <span class="value" id="yaw">0.0°</span></p>
            </div>
            
            <div class="card">
                <h3>Battery</h3>
                <p>Voltage: <span class="value" id="voltage">12.4V</span></p>
                <p>Percentage: <span class="value" id="battery">85%</span></p>
            </div>
            
            <div class="card">
                <h3>GPS</h3>
                <p>Satellites: <span class="value" id="sats">8</span></p>
                <p>Latitude: <span class="value" id="lat">0.000000</span></p>
                <p>Longitude: <span class="value" id="lng">0.000000</span></p>
                <p>Altitude: <span class="value" id="alt">0.0m</span></p>
            </div>
            
            <div class="card">
                <h3>Controls</h3>
                <button class="btn-danger" onclick="toggleArm()">ARM/DISARM</button>
                <button class="btn-primary" onclick="calibrateGyro()">Calibrate Gyro</button>
                <button class="btn-success" onclick="savePID()">Save PID</button>
            </div>
        </div>
    </div>
    
    <script>
        function updateTelemetry() {
            fetch('/api/telemetry')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('roll').textContent = data.roll.toFixed(1) + '°';
                    document.getElementById('pitch').textContent = data.pitch.toFixed(1) + '°';
                    document.getElementById('yaw').textContent = data.yaw.toFixed(1) + '°';
                    document.getElementById('voltage').textContent = data.voltage.toFixed(1) + 'V';
                    document.getElementById('battery').textContent = data.batteryPercent + '%';
                    document.getElementById('sats').textContent = data.gpsSats;
                    document.getElementById('lat').textContent = data.gpsLat.toFixed(6);
                    document.getElementById('lng').textContent = data.gpsLng.toFixed(6);
                    document.getElementById('alt').textContent = data.gpsAlt.toFixed(1) + 'm';
                    document.getElementById('mode').textContent = data.flightMode;
                    document.getElementById('armed').textContent = data.armed ? 'Yes' : 'No';
                })
                .catch(error => console.error('Error:', error));
        }
        
        function toggleArm() {
            fetch('/api/arm', { method: 'POST' });
        }
        
        function calibrateGyro() {
            fetch('/api/calibrate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ sensor: 'gyro' })
            });
        }
        
        function savePID() {
            fetch('/api/save-pid', { method: 'POST' });
        }
        
        setInterval(updateTelemetry, 100);
        updateTelemetry();
    </script>
</body>
</html>
  )HTML";
  
  server.send(200, "text/html", html);
}

void WebInterfaceManager::handleTelemetry() {
  String json = getTelemetryJSON();
  server.send(200, "application/json", json);
}

void WebInterfaceManager::handleConfig() {
  String json = getConfigJSON();
  server.send(200, "application/json", json);
}

void WebInterfaceManager::handlePIDUpdate() {
  if (server.hasArg("plain")) {
    DynamicJsonDocument doc(1024);
    deserializeJson(doc, server.arg("plain"));
    
    String axis = doc["axis"];
    String type = doc["type"];
    float value = doc["value"];
    
    // Update PID values
    if (axis == "roll") {
      if (type == "p") flightPID.setRollPID(value, flightPID.getRollPID().getKi(), flightPID.getRollPID().getKd());
      else if (type == "i") flightPID.setRollPID(flightPID.getRollPID().getKp(), value, flightPID.getRollPID().getKd());
      else if (type == "d") flightPID.setRollPID(flightPID.getRollPID().getKp(), flightPID.getRollPID().getKi(), value);
    }
    // Similar for pitch and yaw...
    
    server.send(200, "application/json", "{\"status\":\"ok\"}");
  } else {
    server.send(400, "application/json", "{\"error\":\"no_data\"}");
  }
}

void WebInterfaceManager::handleModeChange() {
  if (server.hasArg("plain")) {
    DynamicJsonDocument doc(512);
    deserializeJson(doc, server.arg("plain"));
    
    int mode = doc["mode"];
    flightMode.setFlightMode(mode);
    
    server.send(200, "application/json", "{\"status\":\"ok\"}");
  } else {
    server.send(400, "application/json", "{\"error\":\"no_data\"}");
  }
}

void WebInterfaceManager::handleCalibrationStart() {
  if (server.hasArg("plain")) {
    DynamicJsonDocument doc(512);
    deserializeJson(doc, server.arg("plain"));
    
    String sensor = doc["sensor"];
    
    if (sensor == "gyro") {
      sensors.calibrateGyro();
    } else if (sensor == "accel") {
      sensors.calibrateAccel();
    } else if (sensor == "mag") {
      sensors.calibrateMag();
    }
    
    server.send(200, "application/json", "{\"status\":\"calibrating\"}");
  } else {
    server.send(400, "application/json", "{\"error\":\"no_data\"}");
  }
}

void WebInterfaceManager::handleArmDisarm() {
  if (flightPID.isArmed()) {
    flightPID.disarmMotors();
  } else {
    flightPID.armMotors();
  }
  
  server.send(200, "application/json", "{\"status\":\"toggled\"}");
}

void WebInterfaceManager::handleNotFound() {
  server.send(404, "text/plain", "File not found");
}

String WebInterfaceManager::getTelemetryJSON() {
  DynamicJsonDocument doc(2048);
  
  doc["roll"] = sensors.getRoll();
  doc["pitch"] = sensors.getPitch();
  doc["yaw"] = sensors.getYaw();
  doc["rollRate"] = sensors.getRollRate();
  doc["pitchRate"] = sensors.getPitchRate();
  doc["yawRate"] = sensors.getYawRate();
  doc["altitude"] = sensors.getAltitude();
  doc["gpsLat"] = sensors.getGPSLat();
  doc["gpsLng"] = sensors.getGPSLng();
  doc["gpsSats"] = sensors.getGPSSats();
  doc["gpsAlt"] = sensors.getAltitude(); // Using barometric altitude for now
  
  // Battery voltage (simplified)
  float voltage = analogRead(35) * (3.3 / 4095.0) * 4.0; // Assuming voltage divider
  doc["voltage"] = voltage;
  doc["batteryPercent"] = map(voltage * 10, 95, 126, 0, 100); // 9.5V to 12.6V mapped to 0-100%
  
  // RC channels
  JsonArray rcChannels = doc.createNestedArray("rcChannels");
  for (int i = 0; i < 8; i++) {
    rcChannels.add(flightMode.getRCChannel(i));
  }
  
  doc["flightMode"] = flightMode.getFlightModeString();
  doc["flightModeNum"] = flightMode.getFlightMode();
  doc["armed"] = flightPID.isArmed();
  doc["failsafe"] = flightMode.isFailsafeActive();
  
  // Motor outputs
  doc["motorFL"] = flightPID.getMotorFL();
  doc["motorFR"] = flightPID.getMotorFR();
  doc["motorBL"] = flightPID.getMotorBL();
  doc["motorBR"] = flightPID.getMotorBR();
  
  String output;
  serializeJson(doc, output);
  return output;
}

String WebInterfaceManager::getConfigJSON() {
  DynamicJsonDocument doc(1024);
  
  // PID settings
  JsonObject rollPID = doc.createNestedObject("rollPID");
  rollPID["p"] = flightPID.getRollPID().getKp();
  rollPID["i"] = flightPID.getRollPID().getKi();
  rollPID["d"] = flightPID.getRollPID().getKd();

  JsonObject pitchPID = doc.createNestedObject("pitchPID");
  pitchPID["p"] = flightPID.getPitchPID().getKp();
  pitchPID["i"] = flightPID.getPitchPID().getKi();
  pitchPID["d"] = flightPID.getPitchPID().getKd();

  JsonObject yawPID = doc.createNestedObject("yawPID");
  yawPID["p"] = flightPID.getYawPID().getKp();
  yawPID["i"] = flightPID.getYawPID().getKi();
  yawPID["d"] = flightPID.getYawPID().getKd();
  
  doc["firmwareVersion"] = FIRMWARE_VERSION;
  doc["uptime"] = millis();
  doc["freeHeap"] = ESP.getFreeHeap();
  
  String output;
  serializeJson(doc, output);
  return output;
}

void WebInterfaceManager::savePIDSettings() {
  // Save PID settings to preferences
  preferences.putFloat("roll_p", flightPID.getRollPID().getKp());
  preferences.putFloat("roll_i", flightPID.getRollPID().getKi());
  preferences.putFloat("roll_d", flightPID.getRollPID().getKd());

  preferences.putFloat("pitch_p", flightPID.getPitchPID().getKp());
  preferences.putFloat("pitch_i", flightPID.getPitchPID().getKi());
  preferences.putFloat("pitch_d", flightPID.getPitchPID().getKd());

  preferences.putFloat("yaw_p", flightPID.getYawPID().getKp());
  preferences.putFloat("yaw_i", flightPID.getYawPID().getKi());
  preferences.putFloat("yaw_d", flightPID.getYawPID().getKd());

  server.send(200, "application/json", "{\"status\":\"saved\"}");
}

void WebInterfaceManager::loadPIDSettings() {
  // Load PID settings from preferences
  float roll_p = preferences.getFloat("roll_p", DEFAULT_PID_ROLL_P);
  float roll_i = preferences.getFloat("roll_i", DEFAULT_PID_ROLL_I);
  float roll_d = preferences.getFloat("roll_d", DEFAULT_PID_ROLL_D);
  
  float pitch_p = preferences.getFloat("pitch_p", DEFAULT_PID_PITCH_P);
  float pitch_i = preferences.getFloat("pitch_i", DEFAULT_PID_PITCH_I);
  float pitch_d = preferences.getFloat("pitch_d", DEFAULT_PID_PITCH_D);
  
  float yaw_p = preferences.getFloat("yaw_p", DEFAULT_PID_YAW_P);
  float yaw_i = preferences.getFloat("yaw_i", DEFAULT_PID_YAW_I);
  float yaw_d = preferences.getFloat("yaw_d", DEFAULT_PID_YAW_D);
  
  flightPID.setRollPID(roll_p, roll_i, roll_d);
  flightPID.setPitchPID(pitch_p, pitch_i, pitch_d);
  flightPID.setYawPID(yaw_p, yaw_i, yaw_d);
}

void WebInterfaceManager::saveFlightModeSettings() {
  // Save flight mode settings to preferences
  preferences.putInt("flight_mode", flightMode.getFlightMode());
}

void WebInterfaceManager::loadFlightModeSettings() {
  // Load flight mode settings from preferences
  int mode = preferences.getInt("flight_mode", FLIGHT_MODE_STABILIZE);
  flightMode.setFlightMode(mode);
}

