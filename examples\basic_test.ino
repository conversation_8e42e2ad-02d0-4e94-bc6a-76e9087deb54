/*
 * QuadFly v2.0 Basic Test Example
 * 
 * This example demonstrates basic sensor reading and motor control
 * without the full flight control system. Use this for initial
 * hardware testing and troubleshooting.
 * 
 * Hardware Required:
 * - ESP32 Dev Module
 * - GY-86 sensor module
 * - 4 ESCs (optional for motor test)
 * 
 * Instructions:
 * 1. Upload this sketch to your ESP32
 * 2. Open Serial Monitor (115200 baud)
 * 3. Observe sensor readings
 * 4. Uncomment motor test section if needed
 */

#include <Wire.h>
#include <MPU6050_tockn.h>
#include <HMC5883L.h>
#include <MS5611.h>
#include <ESP32Servo.h>

// Pin definitions
#define PIN_SDA 21
#define PIN_SCL 22
#define PIN_MOTOR_FL 33
#define PIN_MOTOR_FR 32
#define PIN_MOTOR_BL 15
#define PIN_MOTOR_BR 19
#define PIN_STATUS_LED 2

// Sensor objects
MPU6050 mpu6050(Wire);
HMC5883L compass;
MS5611 barometer;

// Motor objects (uncomment for motor testing)
// Servo motorFL, motorFR, motorBL, motorBR;

void setup() {
  Serial.begin(115200);
  Serial.println("QuadFly v2.0 Basic Test Starting...");
  
  // Initialize status LED
  pinMode(PIN_STATUS_LED, OUTPUT);
  digitalWrite(PIN_STATUS_LED, HIGH);
  
  // Initialize I2C
  Wire.begin(PIN_SDA, PIN_SCL);
  Serial.println("I2C initialized");
  
  // Initialize MPU6050
  mpu6050.begin();
  mpu6050.calcGyroOffsets(true);
  Serial.println("MPU6050 initialized and calibrated");
  
  // Initialize HMC5883L
  if (compass.begin()) {
    compass.setRange(HMC5883L_RANGE_1_3GA);
    compass.setMeasurementMode(HMC5883L_CONTINOUS);
    compass.setDataRate(HMC5883L_DATARATE_30HZ);
    compass.setSamples(HMC5883L_SAMPLES_8);
    Serial.println("HMC5883L initialized");
  } else {
    Serial.println("HMC5883L initialization failed!");
  }
  
  // Initialize MS5611
  if (barometer.begin()) {
    Serial.println("MS5611 initialized");
  } else {
    Serial.println("MS5611 initialization failed!");
  }
  
  // Initialize motors (uncomment for motor testing)
  /*
  ESP32PWM::allocateTimer(0);
  ESP32PWM::allocateTimer(1);
  ESP32PWM::allocateTimer(2);
  ESP32PWM::allocateTimer(3);
  
  motorFL.attach(PIN_MOTOR_FL, 1000, 2000);
  motorFR.attach(PIN_MOTOR_FR, 1000, 2000);
  motorBL.attach(PIN_MOTOR_BL, 1000, 2000);
  motorBR.attach(PIN_MOTOR_BR, 1000, 2000);
  
  // Set motors to minimum throttle
  motorFL.writeMicroseconds(1000);
  motorFR.writeMicroseconds(1000);
  motorBL.writeMicroseconds(1000);
  motorBR.writeMicroseconds(1000);
  
  Serial.println("Motors initialized");
  */
  
  digitalWrite(PIN_STATUS_LED, LOW);
  Serial.println("Basic test ready!");
  Serial.println("Sensor readings will be displayed every second...");
}

void loop() {
  static unsigned long lastPrint = 0;
  static unsigned long lastBlink = 0;
  static bool ledState = false;
  
  // Update sensors
  mpu6050.update();
  
  // Read barometer
  barometer.read();
  
  // Read magnetometer
  Vector mag = compass.readNormalize();
  
  // Print sensor data every second
  if (millis() - lastPrint >= 1000) {
    Serial.println("=== Sensor Readings ===");
    
    // IMU data
    Serial.print("Gyro (°/s): X=");
    Serial.print(mpu6050.getGyroX(), 2);
    Serial.print(", Y=");
    Serial.print(mpu6050.getGyroY(), 2);
    Serial.print(", Z=");
    Serial.println(mpu6050.getGyroZ(), 2);
    
    Serial.print("Accel (g): X=");
    Serial.print(mpu6050.getAccX(), 2);
    Serial.print(", Y=");
    Serial.print(mpu6050.getAccY(), 2);
    Serial.print(", Z=");
    Serial.println(mpu6050.getAccZ(), 2);
    
    Serial.print("Attitude (°): Roll=");
    Serial.print(mpu6050.getAngleX(), 1);
    Serial.print(", Pitch=");
    Serial.print(mpu6050.getAngleY(), 1);
    Serial.print(", Yaw=");
    Serial.println(mpu6050.getAngleZ(), 1);
    
    // Magnetometer data
    Serial.print("Magnetometer: X=");
    Serial.print(mag.XAxis, 2);
    Serial.print(", Y=");
    Serial.print(mag.YAxis, 2);
    Serial.print(", Z=");
    Serial.println(mag.ZAxis, 2);
    
    // Calculate heading
    float heading = atan2(mag.YAxis, mag.XAxis) * 180 / M_PI;
    if (heading < 0) heading += 360;
    Serial.print("Heading: ");
    Serial.print(heading, 1);
    Serial.println("°");
    
    // Barometer data
    Serial.print("Pressure: ");
    Serial.print(barometer.getPressure(), 2);
    Serial.println(" mbar");
    
    Serial.print("Temperature: ");
    Serial.print(barometer.getTemperature(), 1);
    Serial.println("°C");
    
    Serial.print("Altitude: ");
    Serial.print(barometer.getAltitude(), 1);
    Serial.println(" m");
    
    // System info
    Serial.print("Free heap: ");
    Serial.print(ESP.getFreeHeap());
    Serial.println(" bytes");
    
    Serial.print("Uptime: ");
    Serial.print(millis() / 1000);
    Serial.println(" seconds");
    
    Serial.println();
    
    lastPrint = millis();
  }
  
  // Blink LED every 500ms
  if (millis() - lastBlink >= 500) {
    ledState = !ledState;
    digitalWrite(PIN_STATUS_LED, ledState);
    lastBlink = millis();
  }
  
  // Motor test (uncomment to enable)
  /*
  static unsigned long lastMotorTest = 0;
  static int motorTestPhase = 0;
  
  if (millis() - lastMotorTest >= 2000) {
    switch (motorTestPhase) {
      case 0:
        Serial.println("Motor test: All motors 1100us");
        motorFL.writeMicroseconds(1100);
        motorFR.writeMicroseconds(1100);
        motorBL.writeMicroseconds(1100);
        motorBR.writeMicroseconds(1100);
        break;
      case 1:
        Serial.println("Motor test: Front Left 1200us");
        motorFL.writeMicroseconds(1200);
        motorFR.writeMicroseconds(1000);
        motorBL.writeMicroseconds(1000);
        motorBR.writeMicroseconds(1000);
        break;
      case 2:
        Serial.println("Motor test: Front Right 1200us");
        motorFL.writeMicroseconds(1000);
        motorFR.writeMicroseconds(1200);
        motorBL.writeMicroseconds(1000);
        motorBR.writeMicroseconds(1000);
        break;
      case 3:
        Serial.println("Motor test: Back Left 1200us");
        motorFL.writeMicroseconds(1000);
        motorFR.writeMicroseconds(1000);
        motorBL.writeMicroseconds(1200);
        motorBR.writeMicroseconds(1000);
        break;
      case 4:
        Serial.println("Motor test: Back Right 1200us");
        motorFL.writeMicroseconds(1000);
        motorFR.writeMicroseconds(1000);
        motorBL.writeMicroseconds(1000);
        motorBR.writeMicroseconds(1200);
        break;
      default:
        Serial.println("Motor test: All motors off");
        motorFL.writeMicroseconds(1000);
        motorFR.writeMicroseconds(1000);
        motorBL.writeMicroseconds(1000);
        motorBR.writeMicroseconds(1000);
        motorTestPhase = -1;
        break;
    }
    
    motorTestPhase++;
    lastMotorTest = millis();
  }
  */
  
  delay(10); // Small delay to prevent watchdog timeout
}

/*
 * Expected Output:
 * 
 * If everything is working correctly, you should see:
 * 1. Initialization messages for all sensors
 * 2. Continuous sensor readings every second
 * 3. Reasonable values for all sensors:
 *    - Gyro: Near 0 when stationary
 *    - Accel: Z should be ~1g when level
 *    - Attitude: Should match physical orientation
 *    - Magnetometer: Should change with orientation
 *    - Pressure: Should be reasonable for your altitude
 *    - Temperature: Should match ambient temperature
 * 
 * Troubleshooting:
 * - If sensor initialization fails, check I2C connections
 * - If readings are erratic, check power supply stability
 * - If magnetometer readings are wrong, move away from metal
 * - If barometer readings are wrong, check for air leaks
 */

