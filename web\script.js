// QuadFly Flight Controller Web Interface JavaScript

class QuadFlyWebInterface {
    constructor() {
        this.telemetryInterval = null;
        this.isConnected = false;
        this.currentTab = 'dashboard';
        this.sensorChart = null;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initializeChart();
        this.startTelemetryUpdates();
        this.loadSettings();
    }
    
    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
        
        // PID sliders
        this.setupPIDSliders();
        
        // Flight mode selection
        document.querySelectorAll('input[name="flight-mode"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.changeFlightMode(e.target.value);
            });
        });
        
        // Calibration buttons
        document.getElementById('calibrate-gyro').addEventListener('click', () => {
            this.calibrateSensor('gyro');
        });
        
        document.getElementById('calibrate-accel').addEventListener('click', () => {
            this.calibrateSensor('accel');
        });
        
        document.getElementById('calibrate-mag').addEventListener('click', () => {
            this.calibrateSensor('mag');
        });
        
        // Arm/Disarm button
        document.getElementById('arm-button').addEventListener('click', () => {
            this.toggleArming();
        });
        
        // Settings buttons
        document.getElementById('save-pid').addEventListener('click', () => {
            this.savePIDSettings();
        });
        
        document.getElementById('reset-pid').addEventListener('click', () => {
            this.resetPIDSettings();
        });
        
        document.getElementById('reboot-system').addEventListener('click', () => {
            this.rebootSystem();
        });
        
        document.getElementById('factory-reset').addEventListener('click', () => {
            this.factoryReset();
        });
    }
    
    setupPIDSliders() {
        const pidAxes = ['roll', 'pitch', 'yaw'];
        const pidTypes = ['p', 'i', 'd'];
        
        pidAxes.forEach(axis => {
            pidTypes.forEach(type => {
                const slider = document.getElementById(`${axis}-${type}`);
                const valueSpan = document.getElementById(`${axis}-${type}-value`);
                
                if (slider && valueSpan) {
                    slider.addEventListener('input', (e) => {
                        valueSpan.textContent = e.target.value;
                        this.updatePIDValue(axis, type, e.target.value);
                    });
                }
            });
        });
    }
    
    switchTab(tabName) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        
        // Remove active class from all tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });
        
        // Show selected tab content
        document.getElementById(tabName).classList.add('active');
        
        // Add active class to selected tab button
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        this.currentTab = tabName;
    }
    
    startTelemetryUpdates() {
        this.telemetryInterval = setInterval(() => {
            this.updateTelemetry();
        }, 100); // Update every 100ms
    }
    
    async updateTelemetry() {
        try {
            const response = await fetch('/api/telemetry');
            if (response.ok) {
                const data = await response.json();
                this.updateDashboard(data);
                this.isConnected = true;
                this.updateConnectionStatus(true);
            } else {
                this.isConnected = false;
                this.updateConnectionStatus(false);
            }
        } catch (error) {
            console.error('Telemetry update failed:', error);
            this.isConnected = false;
            this.updateConnectionStatus(false);
            // Use mock data for demonstration
            this.updateDashboard(this.getMockTelemetryData());
        }
    }
    
    updateDashboard(data) {
        // Update attitude display
        this.updateAttitude(data.attitude);
        
        // Update battery display
        this.updateBattery(data.battery);
        
        // Update GPS display
        this.updateGPS(data.gps);
        
        // Update RC inputs
        this.updateRCInputs(data.rc);
        
        // Update flight mode
        this.updateFlightMode(data.flightMode);
        
        // Update armed status
        this.updateArmedStatus(data.armed);
        
        // Update failsafe status
        this.updateFailsafeStatus(data.failsafe);
        
        // Update sensor chart
        this.updateSensorChart(data.sensors);
    }
    
    updateAttitude(attitude) {
        document.getElementById('roll-value').textContent = `${attitude.roll.toFixed(1)}°`;
        document.getElementById('pitch-value').textContent = `${attitude.pitch.toFixed(1)}°`;
        document.getElementById('yaw-value').textContent = `${attitude.yaw.toFixed(1)}°`;
        
        // Update progress bars (normalize to 0-100%)
        const rollPercent = ((attitude.roll + 90) / 180) * 100;
        const pitchPercent = ((attitude.pitch + 90) / 180) * 100;
        const yawPercent = (attitude.yaw / 360) * 100;
        
        document.getElementById('roll-bar').style.width = `${Math.max(0, Math.min(100, rollPercent))}%`;
        document.getElementById('pitch-bar').style.width = `${Math.max(0, Math.min(100, pitchPercent))}%`;
        document.getElementById('yaw-bar').style.width = `${Math.max(0, Math.min(100, yawPercent))}%`;
    }
    
    updateBattery(battery) {
        document.getElementById('battery-voltage').textContent = `${battery.voltage.toFixed(1)}V`;
        document.getElementById('battery-percentage').textContent = `${battery.percentage}%`;
        document.getElementById('battery-level').style.width = `${battery.percentage}%`;
        
        // Change color based on battery level
        const batteryLevel = document.getElementById('battery-level');
        if (battery.percentage > 50) {
            batteryLevel.style.background = 'linear-gradient(90deg, #4CAF50, #8BC34A)';
        } else if (battery.percentage > 20) {
            batteryLevel.style.background = 'linear-gradient(90deg, #FF9800, #FFC107)';
        } else {
            batteryLevel.style.background = 'linear-gradient(90deg, #F44336, #E91E63)';
        }
    }
    
    updateGPS(gps) {
        document.getElementById('gps-sats').textContent = gps.satellites;
        document.getElementById('gps-lat').textContent = gps.latitude.toFixed(6);
        document.getElementById('gps-lng').textContent = gps.longitude.toFixed(6);
        document.getElementById('gps-alt').textContent = `${gps.altitude.toFixed(1)}m`;
    }
    
    updateRCInputs(rc) {
        const channels = ['throttle', 'roll', 'pitch', 'yaw'];
        channels.forEach((channel, index) => {
            const value = rc.channels[index];
            document.getElementById(`${channel === 'roll' ? 'roll-rc' : channel === 'pitch' ? 'pitch-rc' : channel === 'yaw' ? 'yaw-rc' : channel}-value`).textContent = value;
            
            // Update progress bar (normalize 1000-2000 to 0-100%)
            const percent = ((value - 1000) / 1000) * 100;
            const barId = channel === 'roll' ? 'roll-rc-bar' : channel === 'pitch' ? 'pitch-rc-bar' : channel === 'yaw' ? 'yaw-rc-bar' : `${channel}-bar`;
            document.getElementById(barId).style.width = `${Math.max(0, Math.min(100, percent))}%`;
        });
    }
    
    updateFlightMode(flightMode) {
        const modeNames = ['Manual', 'Stabilize', 'Altitude Hold', 'GPS Hold', 'Acro'];
        document.querySelector('#flight-mode span').textContent = modeNames[flightMode] || 'Unknown';
        
        // Update mode description
        const descriptions = [
            'Manual mode provides direct control with no stabilization assistance.',
            'Stabilize mode provides auto-leveling assistance. The aircraft will automatically return to level flight when the sticks are centered.',
            'Altitude Hold mode maintains current altitude using the barometric sensor while providing stabilization.',
            'GPS Hold mode maintains current position using GPS while providing altitude hold and stabilization.',
            'Acro mode provides direct rate control for advanced aerobatic maneuvers.'
        ];
        
        document.getElementById('mode-description').innerHTML = `<p>${descriptions[flightMode] || 'Unknown flight mode.'}</p>`;
    }
    
    updateArmedStatus(armed) {
        const armedStatus = document.querySelector('#armed-status span');
        const armButton = document.getElementById('arm-button');
        
        if (armed) {
            armedStatus.textContent = 'Armed';
            armedStatus.parentElement.style.color = '#f44336';
            armButton.textContent = 'DISARM MOTORS';
            armButton.classList.add('armed');
        } else {
            armedStatus.textContent = 'Disarmed';
            armedStatus.parentElement.style.color = '#4CAF50';
            armButton.innerHTML = '<i class="fas fa-power-off"></i> ARM MOTORS';
            armButton.classList.remove('armed');
        }
    }
    
    updateFailsafeStatus(failsafe) {
        const failsafeStatus = document.getElementById('failsafe-status');
        if (failsafe) {
            failsafeStatus.textContent = 'Failsafe: ACTIVE';
            failsafeStatus.parentElement.style.background = 'rgba(244, 67, 54, 0.1)';
            failsafeStatus.parentElement.querySelector('i').style.color = '#f44336';
        } else {
            failsafeStatus.textContent = 'Failsafe: OK';
            failsafeStatus.parentElement.style.background = 'rgba(76, 175, 80, 0.1)';
            failsafeStatus.parentElement.querySelector('i').style.color = '#4CAF50';
        }
    }
    
    updateConnectionStatus(connected) {
        const connectionStatus = document.querySelector('#connection-status span');
        if (connected) {
            connectionStatus.textContent = 'Connected';
            connectionStatus.parentElement.style.color = '#4CAF50';
        } else {
            connectionStatus.textContent = 'Disconnected';
            connectionStatus.parentElement.style.color = '#f44336';
        }
    }
    
    initializeChart() {
        const canvas = document.getElementById('sensor-chart');
        const ctx = canvas.getContext('2d');
        
        // Simple chart implementation
        this.sensorChart = {
            canvas: canvas,
            ctx: ctx,
            data: {
                gyro: [],
                accel: [],
                mag: []
            },
            maxPoints: 50
        };
    }
    
    updateSensorChart(sensors) {
        const chart = this.sensorChart;
        const ctx = chart.ctx;
        
        // Add new data points
        chart.data.gyro.push(sensors.gyro.x);
        chart.data.accel.push(sensors.accel.x);
        chart.data.mag.push(sensors.mag.x);
        
        // Limit data points
        if (chart.data.gyro.length > chart.maxPoints) {
            chart.data.gyro.shift();
            chart.data.accel.shift();
            chart.data.mag.shift();
        }
        
        // Clear canvas
        ctx.clearRect(0, 0, chart.canvas.width, chart.canvas.height);
        
        // Draw chart
        this.drawChart(chart);
    }
    
    drawChart(chart) {
        const ctx = chart.ctx;
        const width = chart.canvas.width;
        const height = chart.canvas.height;
        
        // Draw grid
        ctx.strokeStyle = '#e0e0e0';
        ctx.lineWidth = 1;
        
        for (let i = 0; i <= 10; i++) {
            const y = (height / 10) * i;
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }
        
        // Draw data lines
        const colors = ['#667eea', '#4CAF50', '#FF9800'];
        const datasets = [chart.data.gyro, chart.data.accel, chart.data.mag];
        
        datasets.forEach((data, index) => {
            if (data.length < 2) return;
            
            ctx.strokeStyle = colors[index];
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            data.forEach((value, i) => {
                const x = (width / (chart.maxPoints - 1)) * i;
                const y = height / 2 + (value * height / 4); // Normalize to chart height
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
        });
    }
    
    async updatePIDValue(axis, type, value) {
        try {
            await fetch('/api/pid', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    axis: axis,
                    type: type,
                    value: parseFloat(value)
                })
            });
        } catch (error) {
            console.error('PID update failed:', error);
        }
    }
    
    async changeFlightMode(mode) {
        try {
            await fetch('/api/flight-mode', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    mode: parseInt(mode)
                })
            });
        } catch (error) {
            console.error('Flight mode change failed:', error);
        }
    }
    
    async calibrateSensor(sensor) {
        const statusElement = document.getElementById('calibration-status');
        statusElement.textContent = `Calibrating ${sensor}...`;
        statusElement.style.color = '#FF9800';
        
        try {
            const response = await fetch('/api/calibrate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sensor: sensor
                })
            });
            
            if (response.ok) {
                statusElement.textContent = `${sensor} calibration complete!`;
                statusElement.style.color = '#4CAF50';
            } else {
                statusElement.textContent = `${sensor} calibration failed!`;
                statusElement.style.color = '#f44336';
            }
        } catch (error) {
            console.error('Calibration failed:', error);
            statusElement.textContent = `${sensor} calibration failed!`;
            statusElement.style.color = '#f44336';
        }
        
        setTimeout(() => {
            statusElement.textContent = 'Ready for calibration';
            statusElement.style.color = '#667eea';
        }, 3000);
    }
    
    async toggleArming() {
        try {
            await fetch('/api/arm', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        } catch (error) {
            console.error('Arming toggle failed:', error);
        }
    }
    
    async savePIDSettings() {
        try {
            const pidData = {};
            const axes = ['roll', 'pitch', 'yaw'];
            const types = ['p', 'i', 'd'];
            
            axes.forEach(axis => {
                pidData[axis] = {};
                types.forEach(type => {
                    const slider = document.getElementById(`${axis}-${type}`);
                    pidData[axis][type] = parseFloat(slider.value);
                });
            });
            
            await fetch('/api/save-pid', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(pidData)
            });
            
            alert('PID settings saved successfully!');
        } catch (error) {
            console.error('Save PID failed:', error);
            alert('Failed to save PID settings!');
        }
    }
    
    resetPIDSettings() {
        // Reset to default values
        const defaults = {
            roll: { p: 1.0, i: 0.1, d: 0.05 },
            pitch: { p: 1.0, i: 0.1, d: 0.05 },
            yaw: { p: 2.0, i: 0.2, d: 0.0 }
        };
        
        Object.keys(defaults).forEach(axis => {
            Object.keys(defaults[axis]).forEach(type => {
                const slider = document.getElementById(`${axis}-${type}`);
                const valueSpan = document.getElementById(`${axis}-${type}-value`);
                slider.value = defaults[axis][type];
                valueSpan.textContent = defaults[axis][type];
            });
        });
    }
    
    async rebootSystem() {
        if (confirm('Are you sure you want to reboot the system?')) {
            try {
                await fetch('/api/reboot', {
                    method: 'POST'
                });
                alert('System is rebooting...');
            } catch (error) {
                console.error('Reboot failed:', error);
            }
        }
    }
    
    async factoryReset() {
        if (confirm('Are you sure you want to perform a factory reset? This will erase all settings!')) {
            try {
                await fetch('/api/factory-reset', {
                    method: 'POST'
                });
                alert('Factory reset complete. System is rebooting...');
            } catch (error) {
                console.error('Factory reset failed:', error);
            }
        }
    }
    
    loadSettings() {
        // Load saved settings from localStorage or server
        const savedTab = localStorage.getItem('quadfly-current-tab');
        if (savedTab) {
            this.switchTab(savedTab);
        }
    }
    
    getMockTelemetryData() {
        // Mock data for demonstration
        return {
            attitude: {
                roll: Math.sin(Date.now() / 1000) * 15,
                pitch: Math.cos(Date.now() / 1000) * 10,
                yaw: (Date.now() / 100) % 360
            },
            battery: {
                voltage: 12.4,
                percentage: 85
            },
            gps: {
                satellites: 8,
                latitude: 40.7128,
                longitude: -74.0060,
                altitude: 10.5
            },
            rc: {
                channels: [1500, 1500, 1500, 1500, 1000, 1000, 1000, 1000]
            },
            sensors: {
                gyro: { x: Math.random() * 2 - 1, y: Math.random() * 2 - 1, z: Math.random() * 2 - 1 },
                accel: { x: Math.random() * 2 - 1, y: Math.random() * 2 - 1, z: Math.random() * 2 - 1 },
                mag: { x: Math.random() * 2 - 1, y: Math.random() * 2 - 1, z: Math.random() * 2 - 1 }
            },
            flightMode: 1,
            armed: false,
            failsafe: false
        };
    }
}

// Initialize the web interface when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new QuadFlyWebInterface();
});

// Save current tab to localStorage when switching
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('tab-button')) {
        localStorage.setItem('quadfly-current-tab', e.target.dataset.tab);
    }
});

