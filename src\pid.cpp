#include "pid.h"
#include "pins.h"
#include "sensors.h"
#include <ESP32Servo.h>
#include <Arduino.h>

FlightPIDManager flightPID;

// Servo objects for motors
Servo motorFL_servo;
Servo motorFR_servo;
Servo motorBL_servo;
Servo motorBR_servo;

PIDController::PIDController(float p, float i, float d) {
  kp = p;
  ki = i;
  kd = d;
  integral = 0;
  previousError = 0;
  integralLimit = 400; // Default integral limit
  lastTime = micros();
}

void PIDController::setGains(float p, float i, float d) {
  kp = p;
  ki = i;
  kd = d;
}

void PIDController::setIntegralLimit(float limit) {
  integralLimit = limit;
}

float PIDController::compute(float setpoint, float input) {
  unsigned long currentTime = micros();
  float dt = (currentTime - lastTime) / 1000000.0; // Convert to seconds
  lastTime = currentTime;
  
  float error = setpoint - input;
  integral += error * dt;
  
  // Limit integral to prevent windup
  if (integral > integralLimit) integral = integralLimit;
  else if (integral < -integralLimit) integral = -integralLimit;
  
  float derivative = (error - previousError) / dt;
  previousError = error;
  
  return kp * error + ki * integral + kd * derivative;
}

void PIDController::reset() {
  integral = 0;
  previousError = 0;
}

FlightPIDManager::FlightPIDManager() : 
  rollPID(DEFAULT_PID_ROLL_P, DEFAULT_PID_ROLL_I, DEFAULT_PID_ROLL_D),
  pitchPID(DEFAULT_PID_PITCH_P, DEFAULT_PID_PITCH_I, DEFAULT_PID_PITCH_D),
  yawPID(DEFAULT_PID_YAW_P, DEFAULT_PID_YAW_I, DEFAULT_PID_YAW_D),
  altitudePID(0.0, 0.0, 0.0) // Altitude PID will be tuned later
{
  motorFL = motorFR = motorBL = motorBR = MOTOR_MIN_PULSE;
  armed = false;
}

void FlightPIDManager::initialize() {
  // Allow allocation of all timers
  ESP32PWM::allocateTimer(0);
  ESP32PWM::allocateTimer(1);
  ESP32PWM::allocateTimer(2);
  ESP32PWM::allocateTimer(3);
  
  motorFL_servo.attach(PIN_MOTOR_FRONT_LEFT, MOTOR_MIN_PULSE, MOTOR_MAX_PULSE);
  motorFR_servo.attach(PIN_MOTOR_FRONT_RIGHT, MOTOR_MIN_PULSE, MOTOR_MAX_PULSE);
  motorBL_servo.attach(PIN_MOTOR_BACK_LEFT, MOTOR_MIN_PULSE, MOTOR_MAX_PULSE);
  motorBR_servo.attach(PIN_MOTOR_BACK_RIGHT, MOTOR_MIN_PULSE, MOTOR_MAX_PULSE);
  
  motorFL_servo.writeMicroseconds(MOTOR_MIN_PULSE);
  motorFR_servo.writeMicroseconds(MOTOR_MIN_PULSE);
  motorBL_servo.writeMicroseconds(MOTOR_MIN_PULSE);
  motorBR_servo.writeMicroseconds(MOTOR_MIN_PULSE);
}

void FlightPIDManager::update(float rollSetpoint, float pitchSetpoint, float yawSetpoint, 
                            float altitudeSetpoint, int throttle) {
  if (!armed) {
    motorFL = motorFR = motorBL = motorBR = MOTOR_MIN_PULSE;
    return;
  }
  
  // Get current sensor readings
  float currentRoll = sensors.getRoll();
  float currentPitch = sensors.getPitch();
  float currentYaw = sensors.getYaw();
  float currentAltitude = sensors.getAltitude();
  
  // Compute PID outputs
  float rollOutput = rollPID.compute(rollSetpoint, currentRoll);
  float pitchOutput = pitchPID.compute(pitchSetpoint, currentPitch);
  float yawOutput = yawPID.compute(yawSetpoint, currentYaw);
  float altitudeOutput = altitudePID.compute(altitudeSetpoint, currentAltitude);
  
  // Apply motor mixing
  motorMixing(rollOutput, pitchOutput, yawOutput, throttle + altitudeOutput);
  constrainMotors();
}

void FlightPIDManager::updateMotors() {
  motorFL_servo.writeMicroseconds(motorFL);
  motorFR_servo.writeMicroseconds(motorFR);
  motorBL_servo.writeMicroseconds(motorBL);
  motorBR_servo.writeMicroseconds(motorBR);
}

void FlightPIDManager::setRollPID(float p, float i, float d) {
  rollPID.setGains(p, i, d);
}

void FlightPIDManager::setPitchPID(float p, float i, float d) {
  pitchPID.setGains(p, i, d);
}

void FlightPIDManager::setYawPID(float p, float i, float d) {
  yawPID.setGains(p, i, d);
}

void FlightPIDManager::setAltitudePID(float p, float i, float d) {
  altitudePID.setGains(p, i, d);
}

void FlightPIDManager::armMotors() {
  armed = true;
  rollPID.reset();
  pitchPID.reset();
  yawPID.reset();
  altitudePID.reset();
  Serial.println("Motors Armed!");
}

void FlightPIDManager::disarmMotors() {
  armed = false;
  motorFL = motorFR = motorBL = motorBR = MOTOR_MIN_PULSE;
  updateMotors();
  Serial.println("Motors Disarmed!");
}

bool FlightPIDManager::isArmed() {
  return armed;
}

void FlightPIDManager::motorMixing(float roll, float pitch, float yaw, int throttle) {
  // Basic X-quad mixing
  motorFL = throttle - pitch + roll - yaw;
  motorFR = throttle - pitch - roll + yaw;
  motorBL = throttle + pitch + roll + yaw;
  motorBR = throttle + pitch - roll - yaw;
}

void FlightPIDManager::constrainMotors() {
  motorFL = constrain(motorFL, MOTOR_MIN_PULSE, MOTOR_MAX_PULSE);
  motorFR = constrain(motorFR, MOTOR_MIN_PULSE, MOTOR_MAX_PULSE);
  motorBL = constrain(motorBL, MOTOR_MIN_PULSE, MOTOR_MAX_PULSE);
  motorBR = constrain(motorBR, MOTOR_MIN_PULSE, MOTOR_MAX_PULSE);
}

void FlightPIDManager::resetPIDs() {
  rollPID.reset();
  pitchPID.reset();
  yawPID.reset();
  altitudePID.reset();
}


