#ifndef WEB_INTERFACE_H
#define WEB_INTERFACE_H

#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>
#include <Preferences.h>
#include "config.h"

class WebInterfaceManager {
private:
  WebServer server;
  Preferences preferences;
  
  unsigned long lastTelemetryUpdate;
  unsigned long telemetryUpdateInterval;
  
public:
  WebInterfaceManager();
  
  bool initialize();
  void update();
  void handleClient();
  
  // Web server handlers
  void handleRoot();
  void handleTelemetry();
  void handleConfig();
  void handleCalibration();
  void handleArming();
  void handleNotFound();
  
  // Configuration management
  void savePIDSettings();
  void loadPIDSettings();
  void saveFlightModeSettings();
  void loadFlightModeSettings();
  
  // Telemetry data
  String getTelemetryJSON();
  String getConfigJSON();
  
private:
  void setupRoutes();
  void setupWiFi();
  bool setupSPIFFS();
  
  // API handlers
  void handlePIDUpdate();
  void handleModeChange();
  void handleCalibrationStart();
  void handleArmDisarm();
};

extern WebInterfaceManager webInterface;

#endif

