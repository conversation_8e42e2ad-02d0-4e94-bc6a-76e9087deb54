#ifndef FLIGHT_MODES_H
#define FLIGHT_MODES_H

#include "config.h"
#include "sensors.h"
#include "pid.h"

class FlightModeManager {
private:
  int currentMode;
  int rcChannels[8];
  unsigned long lastRCUpdate;
  bool failsafeActive;
  bool armed;
  
  // Setpoints
  float rollSetpoint, pitchSetpoint, yawSetpoint;
  float altitudeSetpoint;
  int throttleSetpoint;
  
  // GPS hold variables
  float gpsHoldLat, gpsHoldLng;
  bool gpsHoldSet;
  
public:
  FlightModeManager();
  
  void initialize();
  void update();
  void updateRCInputs();
  void processFlightMode();
  void checkFailsafe();
  void checkArming();
  
  // Mode switching
  void setFlightMode(int mode);
  int getFlightMode() { return currentMode; }
  const char* getFlightModeString();
  
  // Safety
  bool isArmed() { return armed; }
  bool isFailsafeActive() { return failsafeActive; }
  void activateFailsafe();
  void deactivateFailsafe();
  
  // RC input getters
  int getRCChannel(int channel) { return rcChannels[channel]; }
  int getThrottle() { return throttleSetpoint; }
  
  // Setpoint getters
  float getRollSetpoint() { return rollSetpoint; }
  float getPitchSetpoint() { return pitchSetpoint; }
  float getYawSetpoint() { return yawSetpoint; }
  float getAltitudeSetpoint() { return altitudeSetpoint; }
  
private:
  void manualMode();
  void stabilizeMode();
  void altitudeHoldMode();
  void gpsHoldMode();
  void acroMode();
  
  int readPWM(int pin);
  float mapRCInput(int input, float minOut, float maxOut);
};

extern FlightModeManager flightMode;

#endif

