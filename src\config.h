#ifndef CONFIG_H
#define CONFIG_H

// System Configuration
#define FIRMWARE_VERSION "2.0.0"
#define LOOP_FREQUENCY 250  // Hz
#define LOOP_TIME (1000000 / LOOP_FREQUENCY)  // microseconds

// WiFi Configuration
#define WIFI_SSID "QuadFly_Config"
#define WIFI_PASSWORD "quadfly123"
#define WEB_SERVER_PORT 80

// PID Default Values
#define DEFAULT_PID_ROLL_P 1.0
#define DEFAULT_PID_ROLL_I 0.1
#define DEFAULT_PID_ROLL_D 0.05

#define DEFAULT_PID_PITCH_P 1.0
#define DEFAULT_PID_PITCH_I 0.1
#define DEFAULT_PID_PITCH_D 0.05

#define DEFAULT_PID_YAW_P 2.0
#define DEFAULT_PID_YAW_I 0.2
#define DEFAULT_PID_YAW_D 0.0

// Flight Mode Configuration
#define FLIGHT_MODE_MANUAL 0
#define FLIGHT_MODE_STABILIZE 1
#define FLIGHT_MODE_ALTITUDE_HOLD 2
#define FLIGHT_MODE_GPS_HOLD 3
#define FLIGHT_MODE_ACRO 4

// Safety Configuration
#define FAILSAFE_TIMEOUT 1000  // milliseconds
#define LOW_BATTERY_VOLTAGE 10.5  // volts
#define CRITICAL_BATTERY_VOLTAGE 9.5  // volts
#define ARM_THROTTLE_THRESHOLD 1100  // microseconds
#define DISARM_THROTTLE_THRESHOLD 1050  // microseconds

// Sensor Configuration
#define GYRO_SCALE_FACTOR 131.0  // LSB/°/s for ±250°/s
#define ACCEL_SCALE_FACTOR 16384.0  // LSB/g for ±2g
#define MAG_DECLINATION 0.0  // degrees (adjust for your location)

// Motor Configuration
#define MOTOR_MIN_PULSE 1000  // microseconds
#define MOTOR_MAX_PULSE 2000  // microseconds
#define MOTOR_IDLE_PULSE 1100  // microseconds

// Display Configuration
#define DISPLAY_UPDATE_RATE 10  // Hz
#define DISPLAY_WIDTH 128
#define DISPLAY_HEIGHT 160

#endif

