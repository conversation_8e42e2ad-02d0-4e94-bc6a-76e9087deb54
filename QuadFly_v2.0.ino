/*
 * QuadFly Flight Controller v2.0
 * 
 * A professional-grade open-source flight controller firmware for ESP32
 * 
 * Features:
 * - Real-time sensor fusion (MPU6050, HMC5883L, MS5611)
 * - Advanced PID control system
 * - Multiple flight modes (Manual, Stabilize, Altitude Hold, GPS Hold, Acro)
 * - WiFi web interface for configuration and telemetry
 * - TFT display for status information
 * - Safety systems (failsafe, arming/disarming, battery monitoring)
 * 
 * Hardware Requirements:
 * - ESP32 Dev Module
 * - GY-86 sensor module (MPU6050 + HMC5883L + MS5611)
 * - GPS module (NEO-6M/NEO-M8N)
 * - ST7735 TFT display
 * - 8-channel RC receiver
 * - 4 ESCs and motors
 * 
 * Author: QuadFly Team
 * Version: 2.0.0
 * License: MIT
 */

#include "src/config.h"
#include "src/pins.h"
#include "src/sensors.h"
#include "src/pid.h"
#include "src/flight_modes.h"
#include "src/web_interface.h"
#include "src/display_ui.h"

// Global variables
unsigned long lastLoopTime = 0;
unsigned long loopCounter = 0;
unsigned long lastStatusUpdate = 0;

void setup() {
  Serial.begin(115200);
  Serial.println("QuadFly Flight Controller v2.0 Starting...");
  
  // Initialize status LED
  pinMode(PIN_STATUS_LED, OUTPUT);
  digitalWrite(PIN_STATUS_LED, HIGH);
  
  // Initialize display first for status updates
  Serial.println("Initializing display...");
  if (!display.initialize()) {
    Serial.println("Display initialization failed!");
  }
  
  // Initialize sensors
  Serial.println("Initializing sensors...");
  if (!sensors.initialize()) {
    Serial.println("Sensor initialization failed!");
    while (1) {
      digitalWrite(PIN_STATUS_LED, !digitalRead(PIN_STATUS_LED));
      delay(100);
    }
  }
  
  // Initialize PID controllers
  Serial.println("Initializing PID controllers...");
  flightPID.initialize();
  
  // Initialize flight mode manager
  Serial.println("Initializing flight modes...");
  flightMode.initialize();
  
  // Initialize web interface
  Serial.println("Initializing web interface...");
  if (!webInterface.initialize()) {
    Serial.println("Web interface initialization failed!");
  }
  
  // Load saved settings
  webInterface.loadPIDSettings();
  webInterface.loadFlightModeSettings();
  
  Serial.println("QuadFly Flight Controller v2.0 Ready!");
  Serial.print("Loop frequency: ");
  Serial.print(LOOP_FREQUENCY);
  Serial.println(" Hz");
  
  digitalWrite(PIN_STATUS_LED, LOW);
  lastLoopTime = micros();
}

void loop() {
  unsigned long currentTime = micros();
  
  // Main control loop at specified frequency
  if (currentTime - lastLoopTime >= LOOP_TIME) {
    // Update sensors and sensor fusion
    sensors.update();
    
    // Update flight mode manager (handles RC inputs, failsafe, arming)
    flightMode.update();
    
    // Update display
    display.update();
    
    // Status LED heartbeat
    if (millis() - lastStatusUpdate >= 1000) {
      digitalWrite(PIN_STATUS_LED, !digitalRead(PIN_STATUS_LED));
      lastStatusUpdate = millis();
      
      // Print debug info every second
      Serial.print("Loop: ");
      Serial.print(loopCounter);
      Serial.print(" | Mode: ");
      Serial.print(flightMode.getFlightModeString());
      Serial.print(" | Armed: ");
      Serial.print(flightPID.isArmed() ? "YES" : "NO");
      Serial.print(" | Roll: ");
      Serial.print(sensors.getRoll(), 1);
      Serial.print("° | Pitch: ");
      Serial.print(sensors.getPitch(), 1);
      Serial.print("° | Yaw: ");
      Serial.print(sensors.getYaw(), 1);
      Serial.println("°");
      
      loopCounter = 0;
    }
    
    lastLoopTime = currentTime;
    loopCounter++;
  }
  
  // Handle web interface (non-blocking)
  webInterface.update();
  
  // Small delay to prevent watchdog timeout
  delayMicroseconds(10);
}


