/*
 * I2C Debug Test for QuadFly v2.0
 * 
 * This simple test helps identify I2C communication issues
 * and shows which sensors are actually connected.
 * 
 * Upload this to your ESP32 to diagnose sensor connectivity.
 */

#include <Wire.h>

// Pin definitions (same as QuadFly)
#define PIN_SDA 21
#define PIN_SCL 22

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("=================================");
  Serial.println("QuadFly v2.0 I2C Debug Test");
  Serial.println("=================================");
  
  // Initialize I2C
  Wire.begin(PIN_SDA, PIN_SCL);
  Serial.println("I2C initialized on pins SDA=21, SCL=22");
  Serial.println();
  
  // Scan for I2C devices
  scanI2CDevices();
  
  Serial.println("=================================");
  Serial.println("Expected I2C addresses:");
  Serial.println("0x68 - MPU6050 (IMU)");
  Serial.println("0x1E - HMC5883L (Magnetometer)");
  Serial.println("0x76 or 0x77 - MS5611 (Barometer)");
  Serial.println("=================================");
  Serial.println();
  Serial.println("Test complete. Check the results above.");
  Serial.println("If devices are missing, check your wiring:");
  Serial.println("- VCC to 3.3V (NOT 5V!)");
  Serial.println("- GND to GND");
  Serial.println("- SDA to pin 21");
  Serial.println("- SCL to pin 22");
  Serial.println();
  Serial.println("Restarting scan in 10 seconds...");
}

void loop() {
  delay(10000);
  Serial.println("\n--- Rescanning I2C devices ---");
  scanI2CDevices();
}

void scanI2CDevices() {
  Serial.println("Scanning I2C bus...");
  int deviceCount = 0;
  
  for (byte address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    byte error = Wire.endTransmission();
    
    if (error == 0) {
      Serial.print("✓ I2C device found at address 0x");
      if (address < 16) Serial.print("0");
      Serial.print(address, HEX);
      
      // Identify common devices
      switch (address) {
        case 0x68:
          Serial.print(" (MPU6050 - IMU)");
          break;
        case 0x1E:
          Serial.print(" (HMC5883L - Magnetometer)");
          break;
        case 0x76:
          Serial.print(" (MS5611 - Barometer, CSB=HIGH)");
          break;
        case 0x77:
          Serial.print(" (MS5611 - Barometer, CSB=LOW)");
          break;
        case 0x3C:
        case 0x3D:
          Serial.print(" (OLED Display)");
          break;
        default:
          Serial.print(" (Unknown device)");
          break;
      }
      Serial.println();
      deviceCount++;
    } else if (error == 4) {
      Serial.print("✗ Unknown error at address 0x");
      if (address < 16) Serial.print("0");
      Serial.println(address, HEX);
    }
  }
  
  Serial.println();
  if (deviceCount == 0) {
    Serial.println("❌ No I2C devices found!");
    Serial.println("Check your wiring and power connections.");
  } else {
    Serial.print("✅ Found ");
    Serial.print(deviceCount);
    Serial.println(" I2C device(s)");
  }
  Serial.println();
}
