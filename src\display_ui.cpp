#include "display_ui.h"
#include "sensors.h"
#include "pid.h"
#include "flight_modes.h"

DisplayManager display;

DisplayManager::DisplayManager() : tft(PIN_TFT_CS, PIN_TFT_DC, PIN_TFT_RST) {
  lastUpdate = 0;
  currentScreen = 0;
  totalScreens = 4;
  batteryVoltage = 12.4;
  batteryPercentage = 85;
}

bool DisplayManager::initialize() {
  tft.initR(INITR_BLACKTAB);
  tft.setRotation(1); // Landscape orientation
  tft.fillScreen(ST7735_BLACK);
  
  // Display startup message
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(2);
  displayCenteredText("QuadFly v2.0", 40, ST7735_CYAN);
  tft.setTextSize(1);
  displayCenteredText("Initializing...", 70, ST7735_WHITE);
  
  delay(2000);
  clearScreen();
  
  Serial.println("Display initialized");
  return true;
}

void DisplayManager::update() {
  unsigned long currentTime = millis();
  if (currentTime - lastUpdate >= (1000 / DISPLAY_UPDATE_RATE)) {
    updateBatteryVoltage();
    
    switch (currentScreen) {
      case 0:
        displayMainScreen();
        break;
      case 1:
        displaySensorScreen();
        break;
      case 2:
        displayGPSScreen();
        break;
      case 3:
        displayConfigScreen();
        break;
    }
    
    lastUpdate = currentTime;
  }
}

void DisplayManager::updateBatteryVoltage() {
  batteryVoltage = analogRead(PIN_BATTERY_VOLTAGE) * (3.3 / 4095.0) * 4.0; // Assuming voltage divider
  batteryPercentage = calculateBatteryPercentage(batteryVoltage);
}

void DisplayManager::nextScreen() {
  currentScreen = (currentScreen + 1) % totalScreens;
  clearScreen();
}

void DisplayManager::previousScreen() {
  currentScreen = (currentScreen - 1 + totalScreens) % totalScreens;
  clearScreen();
}

void DisplayManager::setScreen(int screen) {
  if (screen >= 0 && screen < totalScreens) {
    currentScreen = screen;
    clearScreen();
  }
}

void DisplayManager::displayMainScreen() {
  drawHeader();
  
  // Flight mode
  tft.setTextColor(ST7735_CYAN);
  tft.setTextSize(1);
  tft.setCursor(5, 25);
  tft.print("Mode: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(flightMode.getFlightModeString());
  
  // Armed status
  tft.setCursor(5, 40);
  tft.setTextColor(ST7735_CYAN);
  tft.print("Armed: ");
  if (flightPID.isArmed()) {
    tft.setTextColor(ST7735_RED);
    tft.print("YES");
  } else {
    tft.setTextColor(ST7735_GREEN);
    tft.print("NO");
  }
  
  // Attitude display
  tft.setCursor(5, 60);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Roll: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(sensors.getRoll(), 1);
  tft.print("°");
  
  tft.setCursor(5, 75);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Pitch: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(sensors.getPitch(), 1);
  tft.print("°");
  
  tft.setCursor(5, 90);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Yaw: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(sensors.getYaw(), 1);
  tft.print("°");
  
  // Battery status
  displayBatteryStatus();
  
  // Failsafe status
  displayFailsafeStatus();
  
  drawFooter();
}

void DisplayManager::displaySensorScreen() {
  drawHeader();
  
  tft.setTextColor(ST7735_CYAN);
  tft.setTextSize(1);
  tft.setCursor(5, 25);
  tft.print("SENSOR DATA");
  
  // Gyro data
  tft.setCursor(5, 45);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Gyro X: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(sensors.getRollRate(), 1);
  
  tft.setCursor(5, 60);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Gyro Y: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(sensors.getPitchRate(), 1);
  
  tft.setCursor(5, 75);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Gyro Z: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(sensors.getYawRate(), 1);
  
  // Altitude
  tft.setCursor(5, 95);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Alt: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(sensors.getAltitude(), 1);
  tft.print("m");
  
  drawFooter();
}

void DisplayManager::displayGPSScreen() {
  drawHeader();
  
  tft.setTextColor(ST7735_CYAN);
  tft.setTextSize(1);
  tft.setCursor(5, 25);
  tft.print("GPS STATUS");
  
  // GPS satellites
  tft.setCursor(5, 45);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Sats: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(sensors.getGPSSats());
  
  // GPS coordinates
  tft.setCursor(5, 60);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Lat: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(sensors.getGPSLat(), 6);
  
  tft.setCursor(5, 75);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Lng: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(sensors.getGPSLng(), 6);
  
  // GPS status indicator
  displayGPSStatus();
  
  drawFooter();
}

void DisplayManager::displayConfigScreen() {
  drawHeader();
  
  tft.setTextColor(ST7735_CYAN);
  tft.setTextSize(1);
  tft.setCursor(5, 25);
  tft.print("SYSTEM INFO");
  
  // Firmware version
  tft.setCursor(5, 45);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("FW: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(FIRMWARE_VERSION);
  
  // Uptime
  tft.setCursor(5, 60);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Uptime: ");
  tft.setTextColor(ST7735_WHITE);
  unsigned long uptime = millis() / 1000;
  tft.print(uptime / 60);
  tft.print(":");
  if (uptime % 60 < 10) tft.print("0");
  tft.print(uptime % 60);
  
  // Free memory
  tft.setCursor(5, 75);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Mem: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(ESP.getFreeHeap() / 1024);
  tft.print("KB");
  
  // Loop frequency
  tft.setCursor(5, 90);
  tft.setTextColor(ST7735_YELLOW);
  tft.print("Loop: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(LOOP_FREQUENCY);
  tft.print("Hz");
  
  drawFooter();
}

void DisplayManager::displayFlightMode() {
  tft.setTextColor(ST7735_CYAN);
  tft.setTextSize(1);
  tft.setCursor(5, 25);
  tft.print("Mode: ");
  tft.setTextColor(ST7735_WHITE);
  tft.print(flightMode.getFlightModeString());
}

void DisplayManager::displayBatteryStatus() {
  // Battery voltage and percentage
  tft.setCursor(5, 110);
  tft.setTextColor(ST7735_MAGENTA);
  tft.print("Batt: ");
  
  // Color based on battery level
  uint16_t batteryColor = ST7735_GREEN;
  if (batteryPercentage < 50) batteryColor = ST7735_YELLOW;
  if (batteryPercentage < 20) batteryColor = ST7735_RED;
  
  tft.setTextColor(batteryColor);
  tft.print(batteryVoltage, 1);
  tft.print("V (");
  tft.print(batteryPercentage);
  tft.print("%)");
  
  // Battery bar
  int barWidth = 40;
  int barHeight = 6;
  int barX = 80;
  int barY = 110;
  
  tft.drawRect(barX, barY, barWidth, barHeight, ST7735_WHITE);
  int fillWidth = (batteryPercentage * (barWidth - 2)) / 100;
  tft.fillRect(barX + 1, barY + 1, fillWidth, barHeight - 2, batteryColor);
}

void DisplayManager::displayGPSStatus() {
  tft.setCursor(5, 95);
  tft.setTextColor(ST7735_MAGENTA);
  tft.print("GPS: ");
  
  int sats = sensors.getGPSSats();
  uint16_t gpsColor = ST7735_RED;
  if (sats >= 4) gpsColor = ST7735_YELLOW;
  if (sats >= 6) gpsColor = ST7735_GREEN;
  
  tft.setTextColor(gpsColor);
  if (sats >= 4) {
    tft.print("LOCK");
  } else {
    tft.print("NO FIX");
  }
}

void DisplayManager::displayArmedStatus() {
  tft.setTextColor(ST7735_CYAN);
  tft.print("Armed: ");
  if (flightPID.isArmed()) {
    tft.setTextColor(ST7735_RED);
    tft.print("YES");
  } else {
    tft.setTextColor(ST7735_GREEN);
    tft.print("NO");
  }
}

void DisplayManager::displayFailsafeStatus() {
  tft.setCursor(5, 125);
  tft.setTextColor(ST7735_MAGENTA);
  tft.print("FS: ");
  
  if (flightMode.isFailsafeActive()) {
    tft.setTextColor(ST7735_RED);
    tft.print("ACTIVE");
  } else {
    tft.setTextColor(ST7735_GREEN);
    tft.print("OK");
  }
}

void DisplayManager::displayCenteredText(const char* text, int y, uint16_t color) {
  tft.setTextColor(color);
  int textWidth = strlen(text) * 6; // Approximate character width
  int x = (DISPLAY_WIDTH - textWidth) / 2;
  tft.setCursor(x, y);
  tft.print(text);
}

void DisplayManager::displayProgressBar(int x, int y, int width, int height, int progress, uint16_t color) {
  tft.drawRect(x, y, width, height, ST7735_WHITE);
  int fillWidth = (progress * (width - 2)) / 100;
  tft.fillRect(x + 1, y + 1, fillWidth, height - 2, color);
}

void DisplayManager::clearScreen() {
  tft.fillScreen(ST7735_BLACK);
}

void DisplayManager::drawHeader() {
  // Title bar
  tft.fillRect(0, 0, DISPLAY_WIDTH, 18, ST7735_BLUE);
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);
  tft.setCursor(5, 5);
  tft.print("QuadFly v2.0");
  
  // Screen indicator
  tft.setCursor(DISPLAY_WIDTH - 30, 5);
  tft.print(currentScreen + 1);
  tft.print("/");
  tft.print(totalScreens);
}

void DisplayManager::drawFooter() {
  // Footer with navigation hint
  tft.setTextColor(ST7735_WHITE);
  tft.setTextSize(1);
  tft.setCursor(5, DISPLAY_HEIGHT - 10);
  tft.print("Press button to change screen");
}

float DisplayManager::readBatteryVoltage() {
  return analogRead(PIN_BATTERY_VOLTAGE) * (3.3 / 4095.0) * 4.0; // Assuming voltage divider
}

int DisplayManager::calculateBatteryPercentage(float voltage) {
  // Simple linear mapping from voltage to percentage
  // Adjust these values based on your battery type
  float minVoltage = 9.5;  // 0%
  float maxVoltage = 12.6; // 100%
  
  int percentage = ((voltage - minVoltage) / (maxVoltage - minVoltage)) * 100;
  return constrain(percentage, 0, 100);
}

