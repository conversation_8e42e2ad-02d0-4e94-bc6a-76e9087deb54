#ifndef DISPLAY_UI_H
#define DISPLAY_UI_H

#include <Adafruit_GFX.h>
#include <Adafruit_ST7735.h>
#include <SPI.h>
#include "config.h"
#include "pins.h"

class DisplayManager {
private:
  Adafruit_ST7735 tft;
  
  unsigned long lastUpdate;
  int currentScreen;
  int totalScreens;
  
  // Battery monitoring
  float batteryVoltage;
  int batteryPercentage;
  
public:
  DisplayManager();
  
  bool initialize();
  void update();
  void updateBatteryVoltage();
  
  // Screen management
  void nextScreen();
  void previousScreen();
  void setScreen(int screen);
  
  // Display screens
  void displayMainScreen();
  void displaySensorScreen();
  void displayGPSScreen();
  void displayConfigScreen();
  
  // Status indicators
  void displayFlightMode();
  void displayBatteryStatus();
  void displayGPSStatus();
  void displayArmedStatus();
  void displayFailsafeStatus();
  
  // Utility functions
  void displayCenteredText(const char* text, int y, uint16_t color);
  void displayProgressBar(int x, int y, int width, int height, int progress, uint16_t color);
  void clearScreen();
  
private:
  void drawHeader();
  void drawFooter();
  float readBatteryVoltage();
  int calculateBatteryPercentage(float voltage);
};

extern DisplayManager display;

#endif

