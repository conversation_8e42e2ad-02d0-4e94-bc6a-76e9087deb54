# QuadFly v2.0 Installation Guide

This guide provides step-by-step instructions for installing and configuring the QuadFly Flight Controller v2.0 firmware.

## Prerequisites

### Software Requirements
- **Arduino IDE** 1.8.19 or later (or Arduino IDE 2.x)
- **ESP32 Board Package** 2.0.0 or later
- **Git** (for cloning repository)
- **Web browser** (Chrome, Firefox, Safari, or Edge)

### Hardware Requirements
- ESP32 development board
- GY-86 sensor module
- GPS module (NEO-6M/NEO-M8N)
- ST7735 TFT display
- RC receiver (8-channel PWM)
- ESCs and motors
- LiPo battery and power distribution

## Step 1: Arduino IDE Setup

### Install Arduino IDE
1. Download Arduino IDE from [arduino.cc](https://www.arduino.cc/en/software)
2. Install following the platform-specific instructions
3. Launch Arduino IDE

### Install ESP32 Board Package
1. Open **File → Preferences**
2. Add this URL to "Additional Board Manager URLs":
   ```
   https://dl.espressif.com/dl/package_esp32_index.json
   ```
3. Go to **Tools → Board → Boards Manager**
4. Search for "ESP32" and install "ESP32 by Espressif Systems"
5. Select **Tools → Board → ESP32 Arduino → ESP32 Dev Module**

## Step 2: Library Installation

Install the following libraries through **Tools → Manage Libraries**:

### Required Libraries
```
MPU6050_tockn by tockn
HMC5883L by jarzebski
MS5611 by jarzebski
TinyGPS++ by Mikal Hart
Adafruit GFX Library by Adafruit
Adafruit ST7735 and ST7789 Library by Adafruit
ArduinoJson by Benoit Blanchon
ESP32Servo by Kevin Harrington
```

### Installation Steps
1. Open **Tools → Manage Libraries**
2. Search for each library by name
3. Click **Install** for each library
4. Wait for installation to complete

## Step 3: Download Firmware

### Option A: Download ZIP
1. Go to the QuadFly repository
2. Click **Code → Download ZIP**
3. Extract to your Arduino sketches folder

### Option B: Git Clone
```bash
git clone https://github.com/quadfly/quadfly-v2.git
cd quadfly-v2
```

## Step 4: Hardware Assembly

### Wiring Diagram
Connect components according to this pin mapping:

| Component | ESP32 Pin | Wire Color | Notes |
|-----------|-----------|------------|-------|
| **I2C Sensors** | | | |
| SDA | 21 | Blue | Pull-up resistor may be needed |
| SCL | 22 | Yellow | Pull-up resistor may be needed |
| VCC | 3.3V | Red | Stable power supply |
| GND | GND | Black | Common ground |
| **RC Receiver** | | | |
| Throttle | 14 | Orange | PWM signal |
| Roll | 13 | Yellow | PWM signal |
| Pitch | 12 | Green | PWM signal |
| Yaw | 27 | Blue | PWM signal |
| AUX1 | 26 | Purple | Mode switch |
| AUX2 | 25 | Gray | Additional control |
| AUX3 | 36 | White | Additional control |
| AUX4 | 34 | Brown | Additional control |
| **Motors/ESCs** | | | |
| Front Left | 33 | Red | PWM to ESC |
| Front Right | 32 | Blue | PWM to ESC |
| Back Left | 15 | Green | PWM to ESC |
| Back Right | 19 | Yellow | PWM to ESC |
| **TFT Display** | | | |
| CS | 5 | Orange | Chip Select |
| RST | 4 | Yellow | Reset |
| DC | 2 | Green | Data/Command |
| MOSI | 23 | Blue | SPI Data |
| SCK | 18 | Purple | SPI Clock |
| VCC | 3.3V | Red | Power |
| GND | GND | Black | Ground |
| **GPS Module** | | | |
| RX | 16 | Green | GPS receives data |
| TX | 17 | Blue | GPS transmits data |
| VCC | 3.3V | Red | Power |
| GND | GND | Black | Ground |
| **Power** | | | |
| Battery + | VIN | Red | Through voltage regulator |
| Battery - | GND | Black | Common ground |
| Battery Sense | 35 | Orange | Through voltage divider |

### Assembly Tips
1. **Use quality connectors** - JST or Dupont connectors recommended
2. **Secure all connections** - Use heat shrink or electrical tape
3. **Keep wires short** - Minimize electromagnetic interference
4. **Separate power and signal** - Route power wires away from signal wires
5. **Add filtering capacitors** - 100µF on power rails, 0.1µF near ICs

### Power Distribution
```
LiPo Battery (11.1V-14.8V)
    ↓
Power Distribution Board
    ├── ESCs (Battery voltage)
    ├── 5V Regulator → RC Receiver, GPS
    └── 3.3V Regulator → ESP32, Sensors, Display
```

## Step 5: Firmware Configuration

### Open Project
1. Navigate to the QuadFly_v2.0 folder
2. Open **QuadFly_v2.0.ino** in Arduino IDE

### Configure Board Settings
1. **Tools → Board**: ESP32 Dev Module
2. **Tools → Upload Speed**: 921600
3. **Tools → CPU Frequency**: 240MHz (WiFi/BT)
4. **Tools → Flash Frequency**: 80MHz
5. **Tools → Flash Mode**: DIO
6. **Tools → Flash Size**: 4MB (32Mb)
7. **Tools → Partition Scheme**: Default 4MB with spiffs
8. **Tools → Core Debug Level**: None (for production)

### Verify Pin Configuration
Check `src/pins.h` and modify if your wiring differs:
```cpp
// Example modifications
#define PIN_RC_THROTTLE 14  // Change if using different pin
#define PIN_MOTOR_FRONT_LEFT 33  // Adjust motor pins as needed
```

## Step 6: Compile and Upload

### Compilation
1. Click **Verify** button (checkmark icon)
2. Wait for compilation to complete
3. Check for any errors in the output window
4. Resolve any missing library issues

### Upload Process
1. Connect ESP32 to computer via USB
2. Select correct **COM port** in Tools menu
3. Hold **BOOT** button on ESP32 (if required)
4. Click **Upload** button (arrow icon)
5. Wait for upload to complete
6. Monitor serial output for startup messages

### Expected Output
```
QuadFly Flight Controller v2.0 Starting...
Initializing display...
Initializing sensors...
MPU6050 Initialized
HMC5883L Initialized
MS5611 Initialized
GPS Serial Initialized
Initializing PID controllers...
Initializing flight modes...
Initializing web interface...
WiFi Access Point started
SSID: QuadFly_Config
IP address: ***********
Web server started
QuadFly Flight Controller v2.0 Ready!
Loop frequency: 250 Hz
```

## Step 7: Initial Configuration

### Connect to Web Interface
1. **Power on** the flight controller
2. **Connect device** to WiFi network "QuadFly_Config"
3. **Password**: quadfly123
4. **Open browser** and go to http://***********
5. **Verify connection** - you should see the dashboard

### Sensor Calibration

#### Gyroscope Calibration
1. Place aircraft on **level surface**
2. Ensure aircraft is **completely still**
3. Click **Calibrate Gyroscope** in web interface
4. Wait for **calibration complete** message
5. **Do not move** aircraft during calibration

#### Accelerometer Calibration
1. Place aircraft in **6 different orientations**:
   - Level (normal flight position)
   - Nose up (90°)
   - Nose down (90°)
   - Left side down (90°)
   - Right side down (90°)
   - Upside down (180°)
2. Hold each position for **10 seconds**
3. Click **Calibrate Accelerometer**
4. Follow on-screen instructions

#### Magnetometer Calibration
1. **Remove from metal objects** and electronics
2. Click **Calibrate Magnetometer**
3. **Slowly rotate** aircraft in all axes
4. Continue for **60 seconds** or until complete
5. **Avoid magnetic interference** during calibration

### RC Receiver Setup
1. **Bind receiver** to your transmitter
2. **Verify channel mapping** in web interface
3. **Check control directions**:
   - Throttle: Up increases value
   - Roll: Right increases value
   - Pitch: Forward decreases value
   - Yaw: Right increases value
4. **Adjust endpoints** if necessary (1000-2000µs range)

### PID Tuning (Basic)
Start with default values and adjust if needed:

#### Roll/Pitch PID
- **P Gain**: 1.0 (increase for more responsiveness)
- **I Gain**: 0.1 (increase to reduce steady-state error)
- **D Gain**: 0.05 (increase to reduce oscillations)

#### Yaw PID
- **P Gain**: 2.0 (higher than roll/pitch)
- **I Gain**: 0.2 (moderate integral gain)
- **D Gain**: 0.0 (usually not needed for yaw)

### Flight Mode Configuration
1. **Set AUX channel** for mode switching
2. **Test each mode** on the ground:
   - Manual: Direct control
   - Stabilize: Auto-leveling
   - Altitude Hold: Maintains height
   - GPS Hold: Position lock
   - Acro: Rate control

## Step 8: Ground Testing

### Pre-flight Checklist
- [ ] All sensors calibrated
- [ ] RC receiver bound and responding
- [ ] Motor directions correct
- [ ] Propellers installed correctly
- [ ] Battery fully charged
- [ ] Web interface accessible
- [ ] Display showing correct information
- [ ] Failsafe tested (turn off transmitter)

### Motor Test
1. **Remove propellers** for safety
2. **Arm the system** (throttle low + yaw right)
3. **Slowly increase throttle**
4. **Verify all motors spin** in correct direction
5. **Check motor order**: FL, FR, BL, BR
6. **Test motor stopping** (throttle to minimum)
7. **Disarm system** (throttle low + yaw left)

### Sensor Verification
1. **Check attitude display** matches physical orientation
2. **Verify GPS lock** (may take several minutes)
3. **Test battery monitoring** (check voltage reading)
4. **Confirm failsafe activation** (turn off transmitter)

## Step 9: First Flight Preparation

### Safety Precautions
- **Choose open area** away from people and property
- **Check weather conditions** (low wind, good visibility)
- **Have fire extinguisher** available for LiPo safety
- **Inform others** of your flight activities
- **Follow local regulations** and airspace restrictions

### Final Checks
- [ ] Propellers balanced and secure
- [ ] All connections tight
- [ ] Battery voltage adequate (>11.1V for 3S)
- [ ] Control surfaces moving correctly
- [ ] Emergency procedures reviewed
- [ ] Spotter available if required

## Troubleshooting

### Common Issues

**Compilation Errors**
- Verify all libraries installed
- Check ESP32 board package version
- Ensure correct board selected

**Upload Failures**
- Check USB cable and connection
- Try different COM port
- Hold BOOT button during upload
- Reduce upload speed to 115200

**Sensor Not Found**
- Verify I2C connections (SDA/SCL)
- Check power supply (3.3V stable)
- Test with I2C scanner sketch
- Ensure pull-up resistors if needed

**Web Interface Not Accessible**
- Confirm WiFi connection to "QuadFly_Config"
- Check IP address (***********)
- Try different browser
- Restart ESP32

**Motors Not Responding**
- Check ESC calibration
- Verify motor pin connections
- Ensure proper arming sequence
- Test with multimeter for PWM signals

**GPS No Lock**
- Ensure clear sky view
- Check antenna orientation
- Verify baud rate (9600)
- Allow time for cold start (up to 15 minutes)

### Debug Mode
Enable detailed logging by changing in `config.h`:
```cpp
#define DEBUG_MODE 1
```

This will output detailed information to serial monitor for troubleshooting.

## Support

If you encounter issues not covered in this guide:

1. **Check documentation** in the `docs/` folder
2. **Review code comments** for implementation details
3. **Search existing issues** on GitHub
4. **Create new issue** with detailed description
5. **Join community forums** for peer support

## Next Steps

After successful installation:
- Read the **User Manual** for operational procedures
- Study **PID Tuning Guide** for performance optimization
- Review **Safety Guidelines** before first flight
- Explore **Advanced Features** documentation

---

**Remember**: Always prioritize safety and follow proper procedures when working with flight control systems.

