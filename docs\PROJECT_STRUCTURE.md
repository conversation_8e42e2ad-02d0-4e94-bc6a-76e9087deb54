# QuadFly v2.0 Project Structure

This document describes the organization and structure of the QuadFly Flight Controller v2.0 project.

## Directory Structure

```
QuadFly_v2.0/
├── QuadFly_v2.0.ino          # Main Arduino sketch file
├── README.md                  # Project overview and quick start
├── LICENSE                    # MIT license file
├── CHANGELOG.md              # Version history and changes
├── src/                      # Source code directory
│   ├── config.h              # System configuration constants
│   ├── pins.h                # Hardware pin definitions
│   ├── sensors.h/.cpp        # Sensor management and fusion
│   ├── pid.h/.cpp            # PID control system
│   ├── flight_modes.h/.cpp   # Flight mode management
│   ├── web_interface.h/.cpp  # WiFi and web server
│   └── display_ui.h/.cpp     # TFT display management
├── web/                      # Web interface files
│   ├── index.html            # Main web interface
│   ├── style.css             # Styling and layout
│   └── script.js             # Client-side functionality
├── docs/                     # Documentation directory
│   ├── INSTALLATION.md       # Installation guide
│   ├── USER_MANUAL.md        # User manual
│   └── PROJECT_STRUCTURE.md  # This file
├── examples/                 # Example sketches
│   └── basic_test.ino        # Hardware testing example
└── lib/                      # Custom libraries (if any)
```

## Core Components

### Main Sketch (`QuadFly_v2.0.ino`)
The main Arduino sketch that orchestrates all system components:
- System initialization sequence
- Main control loop at 250Hz
- Component integration and coordination
- Error handling and recovery

### Configuration (`src/config.h`)
Central configuration file containing:
- System constants and parameters
- Default PID values
- Safety thresholds
- Hardware specifications
- Feature flags

### Pin Definitions (`src/pins.h`)
Hardware abstraction layer defining:
- GPIO pin assignments
- I2C and SPI pin mappings
- Motor output pins
- Sensor connection pins
- Display interface pins

## Functional Modules

### Sensor Management (`src/sensors.h/.cpp`)
Handles all sensor operations:
- **MPU6050**: Accelerometer and gyroscope
- **HMC5883L**: Magnetometer for heading
- **MS5611**: Barometric pressure sensor
- **GPS**: Position and navigation data
- **Sensor fusion**: Complementary filter implementation
- **Calibration**: Sensor offset and scaling routines

**Key Classes:**
- `SensorManager`: Main sensor interface
- Calibration methods for each sensor type
- Real-time sensor fusion algorithm

### PID Control System (`src/pid.h/.cpp`)
Implements the flight control algorithms:
- **PIDController**: Generic PID implementation
- **FlightPIDManager**: Flight-specific PID management
- **Motor mixing**: Converts control outputs to motor commands
- **Safety limits**: Prevents dangerous control outputs

**Key Features:**
- Per-axis PID tuning (Roll, Pitch, Yaw)
- Integral windup protection
- Derivative filtering
- Motor arming/disarming logic

### Flight Modes (`src/flight_modes.h/.cpp`)
Manages different flight behaviors:
- **Manual**: Direct pilot control
- **Stabilize**: Auto-leveling assistance
- **Altitude Hold**: Barometric altitude control
- **GPS Hold**: Position maintenance
- **Acro**: Rate control for aerobatics

**Key Features:**
- RC input processing
- Failsafe detection and handling
- Mode switching logic
- Safety checks and validation

### Web Interface (`src/web_interface.h/.cpp`)
Provides wireless configuration and telemetry:
- **WiFi Access Point**: Creates "QuadFly_Config" network
- **Web Server**: Serves configuration interface
- **REST API**: Handles configuration requests
- **Real-time data**: Streams telemetry to browser
- **Settings storage**: Persistent configuration

**API Endpoints:**
- `/api/telemetry` - Real-time sensor data
- `/api/config` - System configuration
- `/api/pid` - PID parameter updates
- `/api/calibrate` - Sensor calibration
- `/api/arm` - Motor arming control

### Display Interface (`src/display_ui.h/.cpp`)
Manages the TFT display:
- **Multiple screens**: Status, sensors, GPS, config
- **Real-time updates**: 10Hz refresh rate
- **Status indicators**: Battery, GPS, armed state
- **Navigation**: Screen switching capability

**Display Screens:**
1. Main status (flight mode, attitude, battery)
2. Sensor data (gyro, accel, altitude)
3. GPS information (coordinates, satellites)
4. System configuration (version, uptime, memory)

## Web Interface Components

### Frontend (`web/`)
Modern responsive web interface:

#### HTML Structure (`web/index.html`)
- **Dashboard**: Real-time telemetry display
- **PID Tuning**: Parameter adjustment sliders
- **Flight Modes**: Mode selection and configuration
- **Calibration**: Sensor calibration tools
- **Settings**: System configuration options

#### Styling (`web/style.css`)
- **Responsive design**: Works on desktop and mobile
- **Modern UI**: Gradient backgrounds, smooth animations
- **Card layout**: Organized information display
- **Status indicators**: Color-coded system status

#### Functionality (`web/script.js`)
- **Real-time updates**: WebSocket-like polling
- **Interactive controls**: Sliders, buttons, forms
- **Data visualization**: Charts and graphs
- **Error handling**: Connection and API error management

## Data Flow

### Sensor Data Flow
```
Physical Sensors → I2C/UART → SensorManager → Sensor Fusion → Flight Control
                                    ↓
                            Web Interface ← Display UI
```

### Control Flow
```
RC Transmitter → FlightModeManager → PID Controllers → Motor Mixing → ESCs/Motors
                        ↓
                 Safety Systems → Failsafe Logic
```

### Configuration Flow
```
Web Interface → REST API → WebInterfaceManager → System Components
                    ↓
              Persistent Storage (Preferences)
```

## Code Organization Principles

### Modular Design
- Each major function is in its own module
- Clear interfaces between modules
- Minimal coupling between components
- Easy to test and maintain individual parts

### Object-Oriented Structure
- Classes encapsulate related functionality
- Public interfaces hide implementation details
- Constructor/destructor pattern for resource management
- Consistent naming conventions

### Error Handling
- Graceful degradation on sensor failures
- Comprehensive error reporting
- Recovery mechanisms where possible
- Safe defaults for all parameters

### Performance Considerations
- 250Hz main control loop
- Non-blocking web interface updates
- Efficient sensor reading and processing
- Memory-conscious data structures

## Build System

### Arduino IDE Integration
- Standard Arduino sketch structure
- Library dependencies clearly documented
- Board-specific configurations included
- Easy compilation and upload process

### Dependencies
External libraries required:
- `MPU6050_tockn` - IMU sensor interface
- `HMC5883L` - Magnetometer interface
- `MS5611` - Barometer interface
- `TinyGPS++` - GPS parsing
- `Adafruit_GFX` - Graphics library
- `Adafruit_ST7735` - Display driver
- `ArduinoJson` - JSON processing
- `ESP32Servo` - Motor control

## Testing Strategy

### Unit Testing
- Individual component testing
- Sensor reading validation
- PID algorithm verification
- Safety system testing

### Integration Testing
- Full system startup sequence
- Component interaction validation
- Performance benchmarking
- Stress testing under load

### Hardware Testing
- Sensor calibration verification
- Motor response testing
- Communication link validation
- Power consumption analysis

## Development Workflow

### Adding New Features
1. Design interface in appropriate header file
2. Implement functionality in corresponding .cpp file
3. Update configuration constants if needed
4. Add web interface controls if applicable
5. Update documentation
6. Test thoroughly before integration

### Debugging Tools
- Serial monitor output (115200 baud)
- Web interface diagnostics
- LED status indicators
- Performance counters

### Version Control
- Semantic versioning (MAJOR.MINOR.PATCH)
- Changelog maintenance
- Tagged releases
- Branch-based development

## Future Extensibility

### Plugin Architecture
The modular design allows for easy addition of:
- New sensor types
- Additional flight modes
- Enhanced safety features
- Advanced navigation algorithms

### Configuration System
Extensible configuration framework supports:
- New parameter types
- Dynamic configuration updates
- Profile management
- Import/export functionality

### Communication Protocols
Designed to support future additions:
- Telemetry radio links
- Ground control station integration
- Mobile app connectivity
- Cloud-based configuration

---

This structure provides a solid foundation for continued development while maintaining code quality, performance, and safety standards.

