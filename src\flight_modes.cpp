#include "flight_modes.h"
#include "sensors.h"
#include "pid.h"

FlightModeManager flightMode;

FlightModeManager::FlightModeManager() {
  currentMode = FLIGHT_MODE_MANUAL;
  for (int i = 0; i < 8; i++) {
    rcChannels[i] = 1500; // Default to center
  }
  lastRCUpdate = 0;
  failsafeActive = false;
  armed = false;
  
  rollSetpoint = 0.0;
  pitchSetpoint = 0.0;
  yawSetpoint = 0.0;
  altitudeSetpoint = 0.0;
  throttleSetpoint = 1000;
  
  gpsHoldLat = 0.0;
  gpsHoldLng = 0.0;
  gpsHoldSet = false;
}

void FlightModeManager::initialize() {
  pinMode(PIN_RC_THROTTLE, INPUT);
  pinMode(PIN_RC_ROLL, INPUT);
  pinMode(PIN_RC_PITCH, INPUT);
  pinMode(PIN_RC_YAW, INPUT);
  pinMode(PIN_RC_AUX1, INPUT);
  pinMode(PIN_RC_AUX2, INPUT);
  pinMode(PIN_RC_AUX3, INPUT);
  pinMode(PIN_RC_AUX4, INPUT);
}

void FlightModeManager::update() {
  updateRCInputs();
  checkFailsafe();
  checkArming();
  checkBatteryVoltage();
  processFlightMode();
}

void FlightModeManager::updateRCInputs() {
  rcChannels[0] = readPWM(PIN_RC_THROTTLE);
  rcChannels[1] = readPWM(PIN_RC_ROLL);
  rcChannels[2] = readPWM(PIN_RC_PITCH);
  rcChannels[3] = readPWM(PIN_RC_YAW);
  rcChannels[4] = readPWM(PIN_RC_AUX1);
  rcChannels[5] = readPWM(PIN_RC_AUX2);
  rcChannels[6] = readPWM(PIN_RC_AUX3);
  rcChannels[7] = readPWM(PIN_RC_AUX4);
  
  // Update last RC update time if valid data is received
  if (rcChannels[0] > 0) { // Assuming throttle is always > 0 for valid signal
    lastRCUpdate = millis();
  }
}

void FlightModeManager::processFlightMode() {
  if (failsafeActive) {
    // In failsafe, motors should be disarmed or slowly brought down
    flightPID.disarmMotors();
    return;
  }
  
  // Map RC inputs to setpoints
  throttleSetpoint = rcChannels[0];
  rollSetpoint = mapRCInput(rcChannels[1], -30.0, 30.0); // Degrees
  pitchSetpoint = mapRCInput(rcChannels[2], -30.0, 30.0); // Degrees
  yawSetpoint = mapRCInput(rcChannels[3], -180.0, 180.0); // Degrees/second for rate mode, or heading for angle mode
  
  // Handle flight mode selection via AUX channels (example)
  if (rcChannels[4] > 1700) {
    setFlightMode(FLIGHT_MODE_GPS_HOLD);
  } else if (rcChannels[4] > 1300) {
    setFlightMode(FLIGHT_MODE_ALTITUDE_HOLD);
  } else if (rcChannels[5] > 1700) {
    setFlightMode(FLIGHT_MODE_ACRO);
  } else if (rcChannels[5] > 1300) {
    setFlightMode(FLIGHT_MODE_STABILIZE);
  } else {
    setFlightMode(FLIGHT_MODE_MANUAL);
  }
  
  switch (currentMode) {
    case FLIGHT_MODE_MANUAL:
      manualMode();
      break;
    case FLIGHT_MODE_STABILIZE:
      stabilizeMode();
      break;
    case FLIGHT_MODE_ALTITUDE_HOLD:
      altitudeHoldMode();
      break;
    case FLIGHT_MODE_GPS_HOLD:
      gpsHoldMode();
      break;
    case FLIGHT_MODE_ACRO:
      acroMode();
      break;
  }
  
  // Update PID with calculated setpoints and throttle
  flightPID.update(rollSetpoint, pitchSetpoint, yawSetpoint, altitudeSetpoint, throttleSetpoint);
  flightPID.updateMotors();
}

void FlightModeManager::checkFailsafe() {
  if (millis() - lastRCUpdate > FAILSAFE_TIMEOUT) {
    if (!failsafeActive) {
      Serial.println("Failsafe Activated: RC Signal Lost!");
      activateFailsafe();
    }
  } else {
    if (failsafeActive) {
      Serial.println("Failsafe Deactivated: RC Signal Recovered.");
      deactivateFailsafe();
    }
  }
}

void FlightModeManager::checkArming() {
  // Arming sequence: Throttle low, Yaw right
  if (!armed && rcChannels[0] < ARM_THROTTLE_THRESHOLD && rcChannels[3] > 1900) {
    flightPID.armMotors();
    armed = true;
  }
  // Disarming sequence: Throttle low, Yaw left
  else if (armed && rcChannels[0] < DISARM_THROTTLE_THRESHOLD && rcChannels[3] < 1100) {
    flightPID.disarmMotors();
    armed = false;
  }
}

void FlightModeManager::setFlightMode(int mode) {
  if (currentMode != mode) {
    currentMode = mode;
    Serial.print("Flight Mode Changed to: ");
    Serial.println(getFlightModeString());
    // Reset PID controllers when changing modes to prevent integral windup
    flightPID.rollPID.reset();
    flightPID.pitchPID.reset();
    flightPID.yawPID.reset();
    flightPID.altitudePID.reset();
  }
}

const char* FlightModeManager::getFlightModeString() {
  switch (currentMode) {
    case FLIGHT_MODE_MANUAL: return "Manual";
    case FLIGHT_MODE_STABILIZE: return "Stabilize";
    case FLIGHT_MODE_ALTITUDE_HOLD: return "Altitude Hold";
    case FLIGHT_MODE_GPS_HOLD: return "GPS Hold";
    case FLIGHT_MODE_ACRO: return "Acro";
    default: return "Unknown";
  }
}

void FlightModeManager::activateFailsafe() {
  failsafeActive = true;
  flightPID.disarmMotors();
}

void FlightModeManager::deactivateFailsafe() {
  failsafeActive = false;
}

void FlightModeManager::manualMode() {
  // In manual mode, setpoints are directly from RC inputs
  // No stabilization or auto-leveling
  rollSetpoint = mapRCInput(rcChannels[1], -30.0, 30.0);
  pitchSetpoint = mapRCInput(rcChannels[2], -30.0, 30.0);
  yawSetpoint = mapRCInput(rcChannels[3], -180.0, 180.0); // Yaw rate
}

void FlightModeManager::stabilizeMode() {
  // In stabilize mode, roll and pitch setpoints are angles
  // Yaw setpoint is rate
  rollSetpoint = mapRCInput(rcChannels[1], -30.0, 30.0); // Target angle
  pitchSetpoint = mapRCInput(rcChannels[2], -30.0, 30.0); // Target angle
  yawSetpoint = mapRCInput(rcChannels[3], -180.0, 180.0); // Target rate
}

void FlightModeManager::altitudeHoldMode() {
  // Altitude hold uses barometer for altitude control
  // Roll, Pitch, Yaw are same as stabilize mode
  stabilizeMode(); // Inherit angle control
  
  // Altitude setpoint is based on initial altitude when mode is engaged
  if (altitudeSetpoint == 0.0) {
    altitudeSetpoint = sensors.getAltitude();
  }
  // Throttle input from RC becomes altitude adjustment
  // This is a simplified approach; a proper altitude hold would use a separate PID for vertical velocity
  float altitudeError = altitudeSetpoint - sensors.getAltitude();
  throttleSetpoint += (int)(altitudeError * 50); // Simple proportional control for altitude
  throttleSetpoint = constrain(throttleSetpoint, 1000, 2000);
}

void FlightModeManager::gpsHoldMode() {
  // GPS hold uses GPS for position control
  // Altitude, Roll, Pitch, Yaw are same as stabilize/altitude hold
  altitudeHoldMode(); // Inherit altitude and angle control
  
  // Set GPS hold target when mode is engaged
  if (!gpsHoldSet) {
    gpsHoldLat = sensors.getGPSLat();
    gpsHoldLng = sensors.getGPSLng();
    gpsHoldSet = true;
  }
  
  // Calculate error from target GPS coordinates
  // This is a very simplified approach; a proper GPS hold requires complex navigation algorithms
  float latError = gpsHoldLat - sensors.getGPSLat();
  float lngError = gpsHoldLng - sensors.getGPSLng();
  
  // Convert errors to roll/pitch adjustments (simplified)
  rollSetpoint += latError * 10000; // Adjust based on latitude error
  pitchSetpoint += lngError * 10000; // Adjust based on longitude error
  
  rollSetpoint = constrain(rollSetpoint, -30.0, 30.0);
  pitchSetpoint = constrain(pitchSetpoint, -30.0, 30.0);
}

void FlightModeManager::acroMode() {
  // Acro mode (rate mode) directly controls angular rates
  rollSetpoint = mapRCInput(rcChannels[1], -300.0, 300.0); // Target roll rate (degrees/sec)
  pitchSetpoint = mapRCInput(rcChannels[2], -300.0, 300.0); // Target pitch rate (degrees/sec)
  yawSetpoint = mapRCInput(rcChannels[3], -300.0, 300.0); // Target yaw rate (degrees/sec)
}

int FlightModeManager::readPWM(int pin) {
  // Reads the pulse width from a PWM input pin
  // This is a blocking function and should be used carefully in a real-time system
  // A better approach would be to use interrupts or a dedicated RC receiver library
  return pulseIn(pin, HIGH, 25000); // Timeout after 25ms
}

float FlightModeManager::mapRCInput(int input, float minOut, float maxOut) {
  // Maps RC input (1000-2000) to a desired output range
  return map(input, 1000, 2000, minOut, maxOut);
}




void FlightModeManager::checkBatteryVoltage() {
  float voltage = analogRead(PIN_BATTERY_VOLTAGE) * (3.3 / 4095.0) * 4.0; // Assuming voltage divider with 1/4 ratio
  if (voltage < CRITICAL_BATTERY_VOLTAGE) {
    Serial.println("CRITICAL BATTERY VOLTAGE! Disarming motors.");
    activateFailsafe(); // Disarm motors on critical battery
  } else if (voltage < LOW_BATTERY_VOLTAGE) {
    Serial.println("Low Battery Warning!");
  }
}


