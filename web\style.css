/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: #667eea;
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.status-bar {
    display: flex;
    gap: 20px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.status-item:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
}

.status-item i {
    color: #667eea;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 10px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    gap: 5px;
}

.tab-button {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tab-button:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.tab-button.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.tab-button i {
    font-size: 1.1rem;
}

/* Main Content */
.main-content {
    min-height: 600px;
}

.tab-content {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Card Styles */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card h3 i {
    color: #667eea;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

/* Attitude Display */
.attitude-display {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.attitude-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.attitude-item label {
    min-width: 60px;
    font-weight: 600;
}

.attitude-item span {
    min-width: 60px;
    font-weight: 500;
    color: #667eea;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 50%;
}

/* Battery Display */
.battery-display {
    display: flex;
    align-items: center;
    gap: 20px;
}

.battery-icon {
    width: 60px;
    height: 30px;
    border: 3px solid #333;
    border-radius: 4px;
    position: relative;
}

.battery-icon::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 8px;
    width: 5px;
    height: 14px;
    background: #333;
    border-radius: 0 2px 2px 0;
}

.battery-level {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    border-radius: 1px;
    width: 85%;
    transition: width 0.3s ease;
}

.battery-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.voltage {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.percentage {
    font-size: 1rem;
    color: #666;
}

/* GPS Display */
.gps-display {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.gps-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.gps-item label {
    font-weight: 600;
}

.gps-item span {
    color: #667eea;
    font-weight: 500;
}

/* RC Display */
.rc-display {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.rc-channel {
    display: flex;
    align-items: center;
    gap: 15px;
}

.rc-channel label {
    min-width: 70px;
    font-weight: 600;
}

.rc-bar {
    flex: 1;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.rc-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 50%;
}

.rc-channel span {
    min-width: 50px;
    color: #667eea;
    font-weight: 500;
}

/* Safety Controls */
.safety-controls {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.arm-button {
    padding: 15px 30px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(45deg, #f44336, #e91e63);
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.arm-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(244, 67, 54, 0.4);
}

.arm-button.armed {
    background: linear-gradient(45deg, #4CAF50, #8BC34A);
}

.safety-status {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.safety-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(76, 175, 80, 0.1);
    border-radius: 8px;
}

.safety-item i {
    color: #4CAF50;
}

/* PID Controls */
.pid-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.pid-controls {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.pid-slider {
    display: flex;
    align-items: center;
    gap: 15px;
}

.pid-slider label {
    min-width: 70px;
    font-weight: 600;
}

.pid-slider input[type="range"] {
    flex: 1;
    height: 8px;
    border-radius: 4px;
    background: #e0e0e0;
    outline: none;
    -webkit-appearance: none;
}

.pid-slider input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.pid-slider span {
    min-width: 50px;
    color: #667eea;
    font-weight: 500;
}

.pid-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

/* Button Styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-warning {
    background: #ffc107;
    color: #333;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Mode Selection */
.modes-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

.mode-selector {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.mode-option {
    position: relative;
}

.mode-option input[type="radio"] {
    display: none;
}

.mode-option label {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.mode-option label:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.mode-option input[type="radio"]:checked + label {
    border-color: #667eea;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    color: #667eea;
}

.mode-option label i {
    font-size: 1.2rem;
    color: #667eea;
}

.mode-description {
    padding: 20px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

/* Calibration Controls */
.calibration-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.calibration-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.calibration-status {
    margin-top: 20px;
    padding: 15px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    color: #667eea;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.settings-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.system-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e0e0e0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-weight: 600;
}

.info-item span {
    color: #667eea;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .status-bar {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .nav-tabs {
        flex-wrap: wrap;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .modes-grid {
        grid-template-columns: 1fr;
    }
    
    .pid-actions {
        flex-direction: column;
    }
    
    .settings-controls {
        flex-direction: column;
    }
}

/* Chart Styles */
#sensor-chart {
    width: 100%;
    height: 200px;
    border-radius: 8px;
}

/* Animation for status updates */
.status-update {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

