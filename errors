C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.cpp: In member function 'void FlightPIDManager::update(float, float, float, float, int)':
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.cpp:92:23: error: 'sensors' was not declared in this scope
   92 |   float currentRoll = sensors.getRoll();
      |                       ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\sensors.cpp: In constructor 'SensorManager::SensorManager()':
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\sensors.cpp:5:66: error: no matching function for call to 'MPU6050::MPU6050()'
    5 | SensorManager::SensorManager() : gpsSerial(PIN_GPS_RX, PIN_GPS_TX) {
      |                                                                  ^
In file included from C:\Users\<USER>\Desktop\QuadFly_v2.0\src\sensors.h:5,
                 from C:\Users\<USER>\Desktop\QuadFly_v2.0\src\sensors.cpp:1:
C:\Users\<USER>\Documents\Arduino\libraries\MPU6050_tockn\src/MPU6050_tockn.h:21:3: note: candidate: 'MPU6050::MPU6050(TwoWire&, float, float)'
   21 |   MPU6050(TwoWire &w, float aC, float gC);
      |   ^~~~~~~
C:\Users\<USER>\Documents\Arduino\libraries\MPU6050_tockn\src/MPU6050_tockn.h:21:3: note:   candidate expects 3 arguments, 0 provided
C:\Users\<USER>\Documents\Arduino\libraries\MPU6050_tockn\src/MPU6050_tockn.h:20:3: note: candidate: 'MPU6050::MPU6050(TwoWire&)'
   20 |   MPU6050(TwoWire &w);
      |   ^~~~~~~
C:\Users\<USER>\Documents\Arduino\libraries\MPU6050_tockn\src/MPU6050_tockn.h:20:3: note:   candidate expects 1 argument, 0 provided
C:\Users\<USER>\Documents\Arduino\libraries\MPU6050_tockn\src/MPU6050_tockn.h:17:7: note: candidate: 'constexpr MPU6050::MPU6050(const MPU6050&)'
   17 | class MPU6050{
      |       ^~~~~~~
C:\Users\<USER>\Documents\Arduino\libraries\MPU6050_tockn\src/MPU6050_tockn.h:17:7: note:   candidate expects 1 argument, 0 provided
C:\Users\<USER>\Documents\Arduino\libraries\MPU6050_tockn\src/MPU6050_tockn.h:17:7: note: candidate: 'constexpr MPU6050::MPU6050(MPU6050&&)'
C:\Users\<USER>\Documents\Arduino\libraries\MPU6050_tockn\src/MPU6050_tockn.h:17:7: note:   candidate expects 1 argument, 0 provided
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\sensors.cpp: In member function 'void SensorManager::updateBarometer()':
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\sensors.cpp:80:24: error: 'class MS5611' has no member named 'getAltitude'
   80 |   altitude = barometer.getAltitude();
      |                        ^~~~~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\display_ui.cpp: In member function 'void DisplayManager::drawFooter()':
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\display_ui.cpp:368:20: error: 'ST7735_GRAY' was not declared in this scope; did you mean 'ST7735_GREEN'?
  368 |   tft.setTextColor(ST7735_GRAY);
      |                    ^~~~~~~~~~~
      |                    ST7735_GREEN
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.cpp: In member function 'void FlightModeManager::update()':
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.cpp:42:3: error: 'checkBatteryVoltage' was not declared in this scope
   42 |   checkBatteryVoltage();
      |   ^~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.cpp: In member function 'void FlightModeManager::setFlightMode(int)':
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.cpp:144:15: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  144 |     flightPID.rollPID.reset();
      |               ^~~~~~~
In file included from C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.h:6,
                 from C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.cpp:1:
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.cpp:145:15: error: 'PIDController FlightPIDManager::pitchPID' is private within this context
  145 |     flightPID.pitchPID.reset();
      |               ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:30:17: note: declared private here
   30 |   PIDController pitchPID;
      |                 ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.cpp:146:15: error: 'PIDController FlightPIDManager::yawPID' is private within this context
  146 |     flightPID.yawPID.reset();
      |               ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:31:17: note: declared private here
   31 |   PIDController yawPID;
      |                 ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.cpp:147:15: error: 'PIDController FlightPIDManager::altitudePID' is private within this context
  147 |     flightPID.altitudePID.reset();
      |               ^~~~~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:32:17: note: declared private here
   32 |   PIDController altitudePID;
      |                 ^~~~~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.cpp: At global scope:
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.cpp:250:6: error: no declaration matches 'void FlightModeManager::checkBatteryVoltage()'
  250 | void FlightModeManager::checkBatteryVoltage() {
      |      ^~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.cpp:250:6: note: no functions named 'void FlightModeManager::checkBatteryVoltage()'
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\flight_modes.h:8:7: note: 'class FlightModeManager' defined here
    8 | class FlightModeManager {
      |       ^~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:208:4: error: missing terminating " character
  208 |   )";
      |    ^~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp: In member function 'void WebInterfaceManager::handleRoot()':
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:160:66: error: 'ARM' was not declared in this scope
  160 |                 <button class="btn-danger" onclick="toggleArm()">ARM/DISARM</button>
      |                                                                  ^~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:160:70: error: 'DISARM' was not declared in this scope
  160 |                 <button class="btn-danger" onclick="toggleArm()">ARM/DISARM</button>
      |                                                                      ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:160:77: error: expected primary-expression before '/' token
  160 |                 <button class="btn-danger" onclick="toggleArm()">ARM/DISARM</button>
      |                                                                             ^
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:160:78: error: 'button' was not declared in this scope
  160 |                 <button class="btn-danger" onclick="toggleArm()">ARM/DISARM</button>
      |                                                                              ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:161:17: error: expected primary-expression before '<' token
  161 |                 <button class="btn-primary" onclick="calibrateGyro()">Calibrate Gyro</button>
      |                 ^
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:187:9: error: expected ',' or ';' before 'function'
  187 |         function toggleArm() {
      |         ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:191:9: error: 'function' was not declared in this scope; did you mean 'std::function'?
  191 |         function calibrateGyro() {
      |         ^~~~~~~~
      |         std::function
In file included from C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/functional:59,
                 from C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\3.2.0\libraries\WiFi\src/WiFiGeneric.h:31,
                 from C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\3.2.0\libraries\WiFi\src/WiFiSTA.h:30,
                 from C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\3.2.0\libraries\WiFi\src/WiFi.h:34,
                 from C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.h:4,
                 from C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:1:
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/xtensa-esp-elf/include/c++/14.2.0/bits/std_function.h:111:11: note: 'std::function' declared here
  111 |     class function;
      |           ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:199:17: error: expected ';' before 'savePID'
  199 |         function savePID() {
      |                 ^~~~~~~~
      |                 ;
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:203:21: error: 'updateTelemetry' was not declared in this scope; did you mean 'handleTelemetry'?
  203 |         setInterval(updateTelemetry, 100);
      |                     ^~~~~~~~~~~~~~~
      |                     handleTelemetry
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:203:9: error: 'setInterval' was not declared in this scope
  203 |         setInterval(updateTelemetry, 100);
      |         ^~~~~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:205:5: error: expected primary-expression before '<' token
  205 |     </script>
      |     ^
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:205:6: error: expected primary-expression before '/' token
  205 |     </script>
      |      ^
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:205:7: error: 'script' was not declared in this scope; did you mean 'crypt'?
  205 |     </script>
      |       ^~~~~~
      |       crypt
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:206:1: error: expected primary-expression before '<' token
  206 | </body>
      | ^
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:206:2: error: expected primary-expression before '/' token
  206 | </body>
      |  ^
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:206:3: error: 'body' was not declared in this scope
  206 | </body>
      |   ^~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:207:1: error: expected primary-expression before '<' token
  207 | </html>
      | ^
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:207:2: error: expected primary-expression before '/' token
  207 | </html>
      |  ^
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:208:3: error: expected primary-expression before ')' token
  208 |   )";
      |   ^
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp: In member function 'void WebInterfaceManager::handlePIDUpdate()':
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:234:62: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  234 |       if (type == "p") flightPID.setRollPID(value, flightPID.rollPID.getKi(), flightPID.rollPID.getKd());
      |                                                              ^~~~~~~
In file included from C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:3:
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:234:89: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  234 |       if (type == "p") flightPID.setRollPID(value, flightPID.rollPID.getKi(), flightPID.rollPID.getKd());
      |                                                                                         ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:235:60: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  235 |       else if (type == "i") flightPID.setRollPID(flightPID.rollPID.getKp(), value, flightPID.rollPID.getKd());
      |                                                            ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:235:94: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  235 |       else if (type == "i") flightPID.setRollPID(flightPID.rollPID.getKp(), value, flightPID.rollPID.getKd());
      |                                                                                              ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:236:60: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  236 |       else if (type == "d") flightPID.setRollPID(flightPID.rollPID.getKp(), flightPID.rollPID.getKi(), value);
      |                                                            ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:236:87: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  236 |       else if (type == "d") flightPID.setRollPID(flightPID.rollPID.getKp(), flightPID.rollPID.getKi(), value);
      |                                                                                       ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp: In member function 'String WebInterfaceManager::getConfigJSON()':
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:342:28: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  342 |   rollPID["p"] = flightPID.rollPID.getKp();
      |                            ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:343:28: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  343 |   rollPID["i"] = flightPID.rollPID.getKi();
      |                            ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:344:28: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  344 |   rollPID["d"] = flightPID.rollPID.getKd();
      |                            ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:347:29: error: 'PIDController FlightPIDManager::pitchPID' is private within this context
  347 |   pitchPID["p"] = flightPID.pitchPID.getKp();
      |                             ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:30:17: note: declared private here
   30 |   PIDController pitchPID;
      |                 ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:348:29: error: 'PIDController FlightPIDManager::pitchPID' is private within this context
  348 |   pitchPID["i"] = flightPID.pitchPID.getKi();
      |                             ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:30:17: note: declared private here
   30 |   PIDController pitchPID;
      |                 ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:349:29: error: 'PIDController FlightPIDManager::pitchPID' is private within this context
  349 |   pitchPID["d"] = flightPID.pitchPID.getKd();
      |                             ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:30:17: note: declared private here
   30 |   PIDController pitchPID;
      |                 ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:352:27: error: 'PIDController FlightPIDManager::yawPID' is private within this context
  352 |   yawPID["p"] = flightPID.yawPID.getKp();
      |                           ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:31:17: note: declared private here
   31 |   PIDController yawPID;
      |                 ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:353:27: error: 'PIDController FlightPIDManager::yawPID' is private within this context
  353 |   yawPID["i"] = flightPID.yawPID.getKi();
      |                           ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:31:17: note: declared private here
   31 |   PIDController yawPID;
      |                 ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:354:27: error: 'PIDController FlightPIDManager::yawPID' is private within this context
  354 |   yawPID["d"] = flightPID.yawPID.getKd();
      |                           ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:31:17: note: declared private here
   31 |   PIDController yawPID;
      |                 ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp: In member function 'void WebInterfaceManager::savePIDSettings()':
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:367:44: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  367 |   preferences.putFloat("roll_p", flightPID.rollPID.getKp());
      |                                            ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:368:44: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  368 |   preferences.putFloat("roll_i", flightPID.rollPID.getKi());
      |                                            ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:369:44: error: 'PIDController FlightPIDManager::rollPID' is private within this context
  369 |   preferences.putFloat("roll_d", flightPID.rollPID.getKd());
      |                                            ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:29:17: note: declared private here
   29 |   PIDController rollPID;
      |                 ^~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:371:45: error: 'PIDController FlightPIDManager::pitchPID' is private within this context
  371 |   preferences.putFloat("pitch_p", flightPID.pitchPID.getKp());
      |                                             ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:30:17: note: declared private here
   30 |   PIDController pitchPID;
      |                 ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:372:45: error: 'PIDController FlightPIDManager::pitchPID' is private within this context
  372 |   preferences.putFloat("pitch_i", flightPID.pitchPID.getKi());
      |                                             ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:30:17: note: declared private here
   30 |   PIDController pitchPID;
      |                 ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:373:45: error: 'PIDController FlightPIDManager::pitchPID' is private within this context
  373 |   preferences.putFloat("pitch_d", flightPID.pitchPID.getKd());
      |                                             ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:30:17: note: declared private here
   30 |   PIDController pitchPID;
      |                 ^~~~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:375:43: error: 'PIDController FlightPIDManager::yawPID' is private within this context
  375 |   preferences.putFloat("yaw_p", flightPID.yawPID.getKp());
      |                                           ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:31:17: note: declared private here
   31 |   PIDController yawPID;
      |                 ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:376:43: error: 'PIDController FlightPIDManager::yawPID' is private within this context
  376 |   preferences.putFloat("yaw_i", flightPID.yawPID.getKi());
      |                                           ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:31:17: note: declared private here
   31 |   PIDController yawPID;
      |                 ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\web_interface.cpp:377:43: error: 'PIDController FlightPIDManager::yawPID' is private within this context
  377 |   preferences.putFloat("yaw_d", flightPID.yawPID.getKd());
      |                                           ^~~~~~
C:\Users\<USER>\Desktop\QuadFly_v2.0\src\pid.h:31:17: note: declared private here
   31 |   PIDController yawPID;
      |                 ^~~~~~
Multiple libraries were found for "HMC5883L.h"
  Used: C:\Users\<USER>\Documents\Arduino\libraries\HMC5883L
  Not used: C:\Users\<USER>\Documents\Arduino\libraries\Grove_3-Axis_Digital_Compass_HMC5883L
exit status 1

Compilation error: 'sensors' was not declared in this scope