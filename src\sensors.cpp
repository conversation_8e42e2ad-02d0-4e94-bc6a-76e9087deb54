#include "sensors.h"
#include <math.h>

SensorManager sensors;

SensorManager::SensorManager() : mpu6050(Wire), gpsSerial(PIN_GPS_RX, PIN_GPS_TX) {
  gyroX = gyroY = gyroZ = 0.0;
  accelX = accelY = accelZ = 0.0;
  magX = magY = magZ = 0.0;
  pressure = altitude = temperature = 0.0;
  gpsLat = gpsLng = gpsAlt = 0.0;
  gpsSats = 0;
  
  gyroOffsetX = gyroOffsetY = gyroOffsetZ = 0.0;
  accelOffsetX = accelOffsetY = accelOffsetZ = 0.0;
  magOffsetX = magOffsetY = magOffsetZ = 0.0;
  
  roll = pitch = yaw = 0.0;
  rollRate = pitchRate = yawRate = 0.0;
  
  lastUpdate = 0;
}

bool SensorManager::initialize() {
  Wire.begin(PIN_SDA, PIN_SCL);
  Serial.println("I2C initialized");

  // Scan for I2C devices first
  scanI2CDevices();

  // MPU6050
  mpu6050.begin();
  mpu6050.calcGyroOffsets(true); // Calibrate gyro on startup
  Serial.println("MPU6050 Initialized");

  // HMC5883L - Check if device is connected before configuring
  if (compass.begin()) {
    compass.setRange(HMC5883L_RANGE_1_3GA);
    compass.setMeasurementMode(HMC5883L_CONTINOUS);
    compass.setDataRate(HMC5883L_DATARATE_30HZ);
    compass.setSamples(HMC5883L_SAMPLES_8);
    Serial.println("HMC5883L Initialized");
  } else {
    Serial.println("HMC5883L initialization failed - device not found");
  }

  // MS5611 - Check if device is connected before configuring
  if (barometer.begin()) {
    Serial.println("MS5611 Initialized");
  } else {
    Serial.println("MS5611 initialization failed - device not found");
  }

  // GPS
  gpsSerial.begin(9600);
  Serial.println("GPS Serial Initialized");

  return true;
}

void SensorManager::update() {
  unsigned long currentTime = micros();
  if (currentTime - lastUpdate >= LOOP_TIME) {
    updateIMU();
    updateBarometer();
    updateGPS();
    sensorFusion();
    lastUpdate = currentTime;
  }
}

void SensorManager::updateIMU() {
  // Update MPU6050 (should always be available)
  mpu6050.update();
  gyroX = mpu6050.getGyroX();
  gyroY = mpu6050.getGyroY();
  gyroZ = mpu6050.getGyroZ();
  accelX = mpu6050.getAccX();
  accelY = mpu6050.getAccY();
  accelZ = mpu6050.getAccZ();

  // Try to read magnetometer (may fail if not connected)
  // Use a simple check to see if compass is responding
  static bool compassAvailable = true;
  if (compassAvailable) {
    Vector norm = compass.readNormalize();
    // Check if we got valid data (not all zeros or NaN)
    if (!isnan(norm.XAxis) && !isnan(norm.YAxis) && !isnan(norm.ZAxis)) {
      magX = norm.XAxis;
      magY = norm.YAxis;
      magZ = norm.ZAxis;
    } else {
      // Compass not responding, disable it
      compassAvailable = false;
      Serial.println("Compass not responding, disabling magnetometer readings");
    }
  }
}

void SensorManager::updateBarometer() {
  static bool barometerAvailable = true;

  if (barometerAvailable) {
    int result = barometer.read();
    if (result == 0) { // MS5611_READ_OK
      pressure = barometer.getPressure();
      temperature = barometer.getTemperature();
      // Calculate altitude from pressure using barometric formula
      // altitude = 44330 * (1 - pow(pressure/1013.25, 1/5.255))
      if (pressure > 0) {
        altitude = 44330.0 * (1.0 - pow(pressure / 1013.25, 0.1903));
      }
    } else {
      // Barometer read failed, disable it
      barometerAvailable = false;
      Serial.println("Barometer not responding, disabling pressure readings");
    }
  }
}

void SensorManager::updateGPS() {
  while (gpsSerial.available() > 0) {
    gps.encode(gpsSerial.read());
  }
  if (gps.location.isUpdated()) {
    gpsLat = gps.location.lat();
    gpsLng = gps.location.lng();
    gpsAlt = gps.altitude.meters();
    gpsSats = gps.satellites.value();
  }
}

void SensorManager::sensorFusion() {
  // Simplified complementary filter for demonstration
  // In a real flight controller, a Kalman filter or Madgwick filter would be used
  
  // Accelerometer angles
  float accelRoll = atan2(accelY, sqrt(accelX * accelX + accelZ * accelZ)) * 180 / M_PI;
  float accelPitch = atan2(-accelX, sqrt(accelY * accelY + accelZ * accelZ)) * 180 / M_PI;
  
  // Gyro rates (convert to degrees/second)
  rollRate = gyroX / GYRO_SCALE_FACTOR;
  pitchRate = gyroY / GYRO_SCALE_FACTOR;
  yawRate = gyroZ / GYRO_SCALE_FACTOR;
  
  // Complementary filter
  float dt = (micros() - lastUpdate) / 1000000.0; // Convert to seconds
  roll = 0.98 * (roll + rollRate * dt) + 0.02 * accelRoll;
  pitch = 0.98 * (pitch + pitchRate * dt) + 0.02 * accelPitch;
  
  // Magnetometer for Yaw (simplified)
  // This needs proper tilt compensation and calibration for accurate results
  float heading = atan2(magY, magX) * 180 / M_PI;
  heading += MAG_DECLINATION; // Adjust for magnetic declination
  if (heading < 0) heading += 360;
  if (heading > 360) heading -= 360;
  yaw = heading;
}

void SensorManager::calibrateGyro() {
  mpu6050.calcGyroOffsets(true);
  gyroOffsetX = mpu6050.getGyroXoffset();
  gyroOffsetY = mpu6050.getGyroYoffset();
  gyroOffsetZ = mpu6050.getGyroZoffset();
  Serial.println("Gyro calibration complete.");
}

void SensorManager::calibrateAccel() {
  // MPU6050_tockn library does not provide direct accel calibration
  // This would typically involve taking multiple readings and calculating offsets
  Serial.println("Accelerometer calibration not implemented in this example.");
}

void SensorManager::calibrateMag() {
  // Magnetometer calibration is complex and usually involves rotating the sensor
  // This would typically involve min/max readings to find hard/soft iron offsets
  Serial.println("Magnetometer calibration not implemented in this example.");
}

void SensorManager::scanI2CDevices() {
  Serial.println("Scanning I2C devices...");
  int deviceCount = 0;

  for (byte address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    byte error = Wire.endTransmission();

    if (error == 0) {
      Serial.print("I2C device found at address 0x");
      if (address < 16) Serial.print("0");
      Serial.print(address, HEX);

      // Identify common devices
      switch (address) {
        case 0x68:
          Serial.print(" (MPU6050)");
          break;
        case 0x1E:
          Serial.print(" (HMC5883L)");
          break;
        case 0x76:
        case 0x77:
          Serial.print(" (MS5611)");
          break;
        default:
          Serial.print(" (Unknown)");
          break;
      }
      Serial.println();
      deviceCount++;
    }
  }

  if (deviceCount == 0) {
    Serial.println("No I2C devices found");
  } else {
    Serial.print("Found ");
    Serial.print(deviceCount);
    Serial.println(" I2C device(s)");
  }
  Serial.println();
}


