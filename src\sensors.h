#ifndef SENSORS_H
#define SENSORS_H

#include <Wire.h>
#include <MPU6050_tockn.h>
#include <HMC5883L.h>
#include <MS5611.h>
#include <TinyGPS++.h>
#include <SoftwareSerial.h>
#include "config.h"
#include "pins.h"

class SensorManager {
private:
  MPU6050 mpu6050;
  HMC5883L compass;
  MS5611 barometer;
  TinyGPSPlus gps;
  SoftwareSerial gpsSerial;
  
  // Sensor data
  float gyroX, gyroY, gyroZ;
  float accelX, accelY, accelZ;
  float magX, magY, magZ;
  float pressure, altitude, temperature;
  float gpsLat, gpsLng, gpsAlt;
  int gpsSats;
  
  // Calibration data
  float gyroOffsetX, gyroOffsetY, gyroOffsetZ;
  float accelOffsetX, accelOffsetY, accelOffsetZ;
  float magOffsetX, magOffsetY, magOffsetZ;
  
  // Sensor fusion variables
  float roll, pitch, yaw;
  float rollRate, pitchRate, yawRate;
  
  unsigned long lastUpdate;
  
public:
  SensorManager();
  bool initialize();
  void update();
  void calibrateGyro();
  void calibrateAccel();
  void calibrateMag();
  
  // Getters
  float getRoll() { return roll; }
  float getPitch() { return pitch; }
  float getYaw() { return yaw; }
  float getRollRate() { return rollRate; }
  float getPitchRate() { return pitchRate; }
  float getYawRate() { return yawRate; }
  float getAltitude() { return altitude; }
  float getGPSLat() { return gpsLat; }
  float getGPSLng() { return gpsLng; }
  int getGPSSats() { return gpsSats; }
  
private:
  void updateIMU();
  void updateBarometer();
  void updateGPS();
  void sensorFusion();
};

extern SensorManager sensors;

#endif

